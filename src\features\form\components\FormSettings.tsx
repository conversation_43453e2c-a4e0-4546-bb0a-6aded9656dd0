import React from 'react';
import { FormDraggableList } from './FormDraggableList';
import { FormItem } from 'types/Form';
import classNames from 'classnames';

interface FormSettingsProps {
    setSchemaForm(schemaForm: FormItem): void;
    schemaForm: FormItem;
}

export default function FormSettings({ schemaForm, setSchemaForm }: FormSettingsProps) {
    const isFormAddRoute = location.pathname.startsWith('/form/add');
    return (
        <div
            className={classNames(
                'tw-border-l tw-border-l-[#e6e6e8] tw-p-4 tw-w-[320px] tw-flex-shrink-0 tw-h-[calc(100vh-374px)] tw-overflow-auto not_click_outside',
                {
                    '!tw-h-[calc(100vh-290px)]': isFormAddRoute,
                }
            )}
        >
            <FormDraggableList schemaForm={schemaForm} setSchemaForm={setSchemaForm} />
        </div>
    );
}
