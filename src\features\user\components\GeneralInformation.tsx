import React from 'react';
import { UserAccount } from '../../../types/User';

interface IProps {
    userInfor: UserAccount;
}

const GeneralInformation = ({ userInfor }: Readonly<IProps>) => (
    <div className="card">
        <div className="card-header border-bottom">
            <h5 className="mb-0 card-title">General Information</h5>
        </div>
        <div className="pt-2 card-body">
            <div className="row">
                <div className="mb-2 col-md-12">
                    <div className="d-flex">
                        <div className="me-2" style={{ width: '120px', color: '#6e6b7b' }}>
                            Full Name:
                        </div>
                        <div style={{ fontWeight: '500' }}>{userInfor.full_name}</div>
                    </div>
                </div>
                <div className="mb-2 col-md-12">
                    <div className="d-flex">
                        <div className="me-2" style={{ width: '120px', color: '#6e6b7b' }}>
                            Position:
                        </div>
                        <div style={{ fontWeight: '500' }}>{userInfor.oms_position_name}</div>
                    </div>
                </div>
                <div className="mb-2 col-md-12">
                    <div className="d-flex">
                        <div className="me-2" style={{ width: '120px', color: '#6e6b7b' }}>
                            Email:
                        </div>
                        <div style={{ fontWeight: '500' }}>{userInfor.email}</div>
                    </div>
                </div>
                <div className="mb-2 col-md-12">
                    <div className="d-flex">
                        <div className="me-2" style={{ width: '120px', color: '#6e6b7b' }}>
                            Organization:
                        </div>
                        <div style={{ fontWeight: '500' }}>{userInfor.oms_organization_name}</div>
                    </div>
                </div>
                <div className="mb-2 col-md-12">
                    <div className="d-flex">
                        <div className="me-2" style={{ width: '120px', color: '#6e6b7b' }}>
                            Employee Code:
                        </div>
                        <div style={{ fontWeight: '500' }}>{userInfor.oms_emp_code}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
);

export default GeneralInformation;
