import SearchForm from 'components/partials/SearchForm';
import { useTranslation } from 'react-i18next';
import { WorkflowStatusNames } from '../../../types/Workflow';

interface IProps {
    isLoading: boolean;
}

export default function SearchWorkflowForm({ isLoading }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <SearchForm
            fields={[
                {
                    name: 'search',
                    type: 'text',
                    label: 'Search workflow version',
                    wrapClassName: 'col-md-4 col-12',
                    placeholder: 'Enter number of version or name',
                },
                {
                    name: 'status',
                    type: 'select',
                    label: 'Select Status',
                    wrapClassName: 'col-md-4 col-12',
                    options: {
                        multiple: true,
                        choices: WorkflowStatusNames.map((item) => ({
                            label: item.name,
                            value: item.id,
                        })),
                    },
                },
            ]}
            isLoading={isLoading}
        />
    );
}
