import { ErrorCategory, ErrorLogDashboard } from '../../../../types/Logs';
import { useNavigate } from 'react-router-dom';

interface IProps {
    errorLogDashboard?: ErrorLogDashboard;
    handleSearchOnDashboard: (errorCategories: string | null) => void;
}

export default function ErrorLogsStatistical({ errorLogDashboard, handleSearchOnDashboard }: Readonly<IProps>) {
    const navigate = useNavigate();

    return (
        <div className="tw-grid tw-grid-cols-1 sm:tw-grid-cols-3 tw-gap-4 tw-w-full">
            <button type="button" className="card tw-w-full tw-text-left" onClick={() => navigate('/error-log')}>
                <div className="card-header">
                    <div>
                        <h2 className="fw-bolder mb-0">{errorLogDashboard?.total}</h2>
                        <p className="card-text">Total Errors</p>
                    </div>
                    <div className="avatar bg-light-info p-50 m-0">
                        <div className="avatar-content">
                            <i className="font-medium-5 bi bi-list-task"></i>
                        </div>
                    </div>
                </div>
            </button>
            <button
                type="button"
                className="card tw-w-full tw-text-left"
                onClick={() => handleSearchOnDashboard(ErrorCategory.INTEGRATION)}
            >
                <div className="card-header">
                    <div>
                        <h2 className="fw-bolder mb-0">{errorLogDashboard?.oms}</h2>
                        <p className="card-text">Integration Errors</p>
                    </div>
                    <div className="avatar bg-light-warning p-50 m-0">
                        <div className="avatar-content">
                            <i className="font-medium-5 bi bi-hourglass-split"></i>
                        </div>
                    </div>
                </div>
            </button>
            <button
                type="button"
                className="card tw-w-full tw-text-left"
                onClick={() =>
                    handleSearchOnDashboard(
                        [
                            ErrorCategory.AREA_MANAGEMENT,
                            ErrorCategory.WORKFLOW,
                            ErrorCategory.REQUEST_MANAGEMENT,
                            ErrorCategory.TASK_MANAGEMENT,
                            ErrorCategory.USER_MANAGEMENT,
                            ErrorCategory.FORM_MANAGEMENT,
                            ErrorCategory.REPORTING,
                            ErrorCategory.NOTIFICATION,
                            ErrorCategory.AUTHENTICATION,
                            ErrorCategory.SYSTEM,
                        ].join(',')
                    )
                }
            >
                <div className="card-header">
                    <div>
                        <h2 className="fw-bolder mb-0">{errorLogDashboard?.system}</h2>
                        <p className="card-text">System Errors</p>
                    </div>
                    <div className="avatar bg-light-danger p-50 m-0">
                        <div className="avatar-content">
                            <i className="font-medium-5 bi bi-hourglass-split"></i>
                        </div>
                    </div>
                </div>
            </button>
        </div>
    );
}
