import { Link, useParams } from 'react-router-dom';
import BpmnView from '../components/BpmnView';
import { Helmet } from 'react-helmet-async';
import ContentHeader from 'components/partials/ContentHeader';
import { useTranslation } from 'react-i18next';

export const WorkflowEdit = () => {
    const { id } = useParams();
    const { t } = useTranslation();
    return (
        <>
            <Helmet title="Edit Workflow" />

            <ContentHeader
                title={
                    <>
                        <Link to="/workflow">Workflow Version</Link>
                    </>
                }
                breadcrumbs={[{ text: ' Edit Workflow' }]}
            />
            <BpmnView id={id || ''} />
        </>
    );
};
