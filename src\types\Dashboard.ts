import { DataList, FilterConfig } from './common';
import { FILTER_CONDITIONS } from '../constants/common';
import { WorkflowInstance } from './Workflow';

export interface OverviewSummary {
    on_tracking: number;
    completed: number;
    cancelled: number;
    overdue: number;
    backlog: number;
}

export interface OverviewSummaryQuery {
    dashboard_moc_status_summary: OverviewSummary;
}

interface BaseSearchDashboard {
    search?: string;
    sort?: string;
    area_id?: string;
    created_at_from?: string;
    created_at_to?: string;
    created_at__range?: string;
    type?: string;
}

export interface SearchOverviewDashboard extends BaseSearchDashboard {
    tracking_status?: string;
}

export type SearchOverviewDashboardParam = {
    [key in keyof SearchOverviewDashboard]: string;
};

export const overviewFilterConfig: FilterConfig = {
    area_id: { key: 'workflow_instance_areas.area_id', operator: FILTER_CONDITIONS.IN },
    created_at_from: { key: 'created_at', operator: FILTER_CONDITIONS.GREATER_OR_EQUAL },
    created_at_to: { key: 'created_at', operator: FILTER_CONDITIONS.LESS_OR_EQUAL },
    type: { key: 'type', operator: FILTER_CONDITIONS.IN },
    tracking_status: { key: 'tracking_status', operator: FILTER_CONDITIONS.IN },
};

export interface SearchProcessDashboard extends BaseSearchDashboard {
    workflow_definition_id?: string;
    health?: string;
    page?: string;
    limit?: string;
}

export type SearchProcessDashboardParam = {
    [key in keyof SearchProcessDashboard]: string;
};

export const processFilterConfig: FilterConfig = {
    area_id: { key: 'workflow_instance_areas.area_id', operator: FILTER_CONDITIONS.IN },
    created_at_from: { key: 'created_at', operator: FILTER_CONDITIONS.GREATER_OR_EQUAL },
    created_at_to: { key: 'created_at', operator: FILTER_CONDITIONS.LESS_OR_EQUAL },
    type: { key: 'type', operator: FILTER_CONDITIONS.IN },
    workflow_definition_id: { key: 'workflow_definition_id', operator: FILTER_CONDITIONS.EQUAL },
    health: { key: 'is_health', operator: FILTER_CONDITIONS.EQUAL },
    page: { key: 'page', operator: FILTER_CONDITIONS.EQUAL },
    limit: { key: 'limit', operator: FILTER_CONDITIONS.EQUAL },
};

export interface OverviewAreaSummary {
    id: string;
    name: string;
    type: string;
    code: string;
    summary: OverviewSummary;
}

export interface OverviewAreaSummaryQuery {
    dashboard_moc_area_summary: OverviewAreaSummary[];
}

export interface OverviewTypeSummary {
    type: string;
    summary: {
        software: number;
        facility_non_process: number;
        facility_process: number;
    };
}

export interface OverviewTypeSummaryQuery {
    dashboard_moc_type_summary: OverviewTypeSummary[];
}

export interface DashboardMocWorkflowInstances extends WorkflowInstance {
    due_date: string;
    overdue: number;
    backlog: number;
}

export interface DashboardMocWorkflowInstancesQuery {
    dashboard_moc_workflow_instances: DataList<DashboardMocWorkflowInstances>;
}

export interface ProcessStatusByStep {
    id: string;
    name: string;
    order: number;
    key: string;
    count: number;
}

export interface ProcessMocByArea {
    id: string;
    name: string;
    type: string;
    code: string;
    stack: ProcessStatusByStep[];
}

export interface TimeLineStackerByStep {
    name: string;
    stack: ProcessStatusByStep[];
}

export interface MocProcessSummary {
    status_by_step: ProcessStatusByStep[];
    area_stacker_by_step: ProcessMocByArea[];
    time_line_stacker_by_step: TimeLineStackerByStep[];
}

export interface MocProcessSummaryQuery {
    dashboard_moc_process_summary: MocProcessSummary;
}

export interface MocHealthSummary {
    overdue_by_step: ProcessStatusByStep[];
    overdue_by_area: ProcessMocByArea[];
    backlog_by_area: ProcessStatusByStep[];
}

export interface MocHealthSummaryQuery {
    dashboard_moc_health_summary: MocHealthSummary;
}
