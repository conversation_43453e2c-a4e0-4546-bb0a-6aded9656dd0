import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Task, TaskStatus } from '../../../types/Task';

interface IProps {
    tasks: Task[];
}

export default function TaskStatistics({ tasks }: Readonly<IProps>) {
    const { t } = useTranslation();

    const totalTasks = useMemo(() => tasks.length, [tasks]);

    const inProgressTasks = useMemo(
        () => tasks.filter((task) => task.status === TaskStatus.IN_PROGRESS).length,
        [tasks]
    );

    const completedTasks = useMemo(() => tasks.filter((task) => task.status === TaskStatus.APPROVED).length, [tasks]);

    return (
        <div className="row">
            <div className="col-lg-4 col-sm-6 col-12">
                <div className="card">
                    <div className="card-header">
                        <div>
                            <h2 className="fw-bolder mb-0">{totalTasks}</h2>
                            <p className="card-text">{t('Total task')}</p>
                        </div>
                        <div className="avatar bg-light-primary p-50 m-0">
                            <div className="avatar-content">
                                <i className="font-medium-5 bi bi-list-task"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div className="col-lg-4 col-sm-6 col-12">
                <div className="card">
                    <div className="card-header">
                        <div>
                            <h2 className="fw-bolder mb-0">{inProgressTasks}</h2>
                            <p className="card-text">{t('My In Progress')}</p>
                        </div>
                        <div className="avatar bg-light-warning p-50 m-0">
                            <div className="avatar-content">
                                <i className="font-medium-5 bi bi-hourglass-split"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div className="col-lg-4 col-sm-6 col-12">
                <div className="card">
                    <div className="card-header">
                        <div>
                            <h2 className="fw-bolder mb-0">{completedTasks}</h2>
                            <p className="card-text">{t('Recently Completed')}</p>
                        </div>
                        <div className="avatar bg-light-success p-50 m-0">
                            <div className="avatar-content">
                                <i className="font-medium-5 bi bi-check-circle"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
