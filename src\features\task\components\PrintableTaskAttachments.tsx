import React, { useRef } from 'react';
import { Printer, Download } from 'react-feather';
import { useReactToPrint } from 'react-to-print';
import jsPDF from 'jspdf';
import TaskAttachmentsSection from './TaskAttachmentsSection';
import { FileAttachment } from '../../../types/Task';
import html2canvas from 'html2canvas';

interface PrintableTaskAttachmentsProps {
    fileAttachments: FileAttachment[];
}

const PrintableTaskAttachments: React.FC<PrintableTaskAttachmentsProps> = ({ fileAttachments }) => {
    const contentRef = useRef<HTMLDivElement>(null);

    // Phương thức 1: S<PERSON> dụng react-to-print để in trực tiếp với page breaks
    const handlePrint = useReactToPrint({
        contentRef,
        documentTitle: 'Task-Attachments',
        pageStyle: `
            @page {
                size: A4;
                margin: 20mm;
            }
            @media print {
                body {
                    -webkit-print-color-adjust: exact;
                    color-adjust: exact;
                    font-size: 12pt;
                    line-height: 1.4;
                }

                /* Chia trang cho mỗi workflow step */
                .workflow-step-section {
                    page-break-inside: avoid;
                    break-inside: avoid;
                    margin-bottom: 20px;
                }

                .workflow-step-section:not(:first-child) {
                    page-break-before: auto;
                    break-before: auto;
                }

                /* Tránh chia trang giữa file items */
                .file-item {
                    page-break-inside: avoid;
                    break-inside: avoid;
                    margin-bottom: 10px;
                }

                /* Header của mỗi step */
                .step-header {
                    page-break-after: avoid;
                    break-after: avoid;
                    margin-bottom: 15px;
                }

                /* Tránh orphan/widow */
                h1, h2, h3, h4, h5, h6 {
                    page-break-after: avoid;
                    break-after: avoid;
                    orphans: 3;
                    widows: 3;
                }

                /* Đảm bảo nội dung không bị cắt */
                .tw-print-container {
                    page-break-inside: auto;
                    break-inside: auto;
                }

                /* Ẩn các element không cần thiết khi in */
                .no-print {
                    display: none !important;
                }
            }
        `,
        onAfterPrint: () => console.log('In thành công!'),
    });

    // Phương thức 2: Sử dụng html2canvas + jsPDF để xuất PDF (tránh lỗi oklch)
    const handleExportPDF = async () => {
        if (!contentRef.current) return;

        try {
            // Tạo một container tạm thời với CSS override
            const tempContainer = document.createElement('div');
            tempContainer.style.position = 'absolute';
            tempContainer.style.left = '-9999px';
            tempContainer.style.top = '0';
            tempContainer.style.width = contentRef.current.offsetWidth + 'px';
            tempContainer.style.backgroundColor = '#ffffff';

            // Clone nội dung
            const clonedContent = contentRef.current.cloneNode(true) as HTMLElement;

            // Thêm CSS override để thay thế oklch colors
            const style = document.createElement('style');
            style.textContent = `
                * {
                    color: #000000 !important;
                    background-color: transparent !important;
                    border-color: #cccccc !important;
                }
                .tw-bg-white {
                    background-color: #ffffff !important;
                }
                .tw-bg-gray-50 {
                    background-color: #f9fafb !important;
                }
                .tw-text-gray-900, .tw-text-gray-800, .tw-text-gray-700 {
                    color: #1f2937 !important;
                }
                .tw-text-gray-600, .tw-text-gray-500 {
                    color: #6b7280 !important;
                }
                .tw-text-gray-400 {
                    color: #9ca3af !important;
                }
                .tw-border-gray-300 {
                    border-color: #d1d5db !important;
                }
                .tw-border-gray-200 {
                    border-color: #e5e7eb !important;
                }
                .tw-text-red-500 { color: #ef4444 !important; }
                .tw-text-blue-500 { color: #3b82f6 !important; }
                .tw-text-green-500 { color: #10b981 !important; }
                .tw-text-orange-500 { color: #f97316 !important; }
                .tw-text-purple-500 { color: #8b5cf6 !important; }
                .tw-text-yellow-500 { color: #eab308 !important; }
                .tw-text-pink-500 { color: #ec4899 !important; }
                .tw-text-indigo-500 { color: #6366f1 !important; }
            `;

            tempContainer.appendChild(style);
            tempContainer.appendChild(clonedContent);
            document.body.appendChild(tempContainer);

            // Đợi một chút để CSS được apply
            await new Promise((resolve) => setTimeout(resolve, 100));

            // Sử dụng html2canvas với container đã được override CSS
            const canvas = await html2canvas(tempContainer, {
                scale: 2,
                useCORS: true,
                allowTaint: false,
                backgroundColor: '#ffffff',
                logging: false,
                ignoreElements: (element: Element) => element.classList?.contains('no-print') || false,
            });

            // Xóa container tạm thời
            document.body.removeChild(tempContainer);

            // Tạo PDF từ canvas (đơn giản hóa - fit vào 1 trang)
            const imgData = canvas.toDataURL('image/png');
            const pdf = new jsPDF({
                orientation: 'portrait',
                unit: 'mm',
                format: 'a4',
            });

            // Kích thước trang A4
            const pdfWidth = pdf.internal.pageSize.getWidth();
            const pdfHeight = pdf.internal.pageSize.getHeight();
            const margin = 10;

            // Tính toán để fit vào trang với tỷ lệ phù hợp
            const availableWidth = pdfWidth - margin * 2;
            const availableHeight = pdfHeight - margin * 2;

            const canvasRatio = canvas.width / canvas.height;
            const pageRatio = availableWidth / availableHeight;

            let finalWidth, finalHeight;

            if (canvasRatio > pageRatio) {
                // Canvas rộng hơn, fit theo chiều rộng
                finalWidth = availableWidth;
                finalHeight = availableWidth / canvasRatio;
            } else {
                // Canvas cao hơn, fit theo chiều cao
                finalHeight = availableHeight;
                finalWidth = availableHeight * canvasRatio;
            }

            const x = (pdfWidth - finalWidth) / 2;
            const y = (pdfHeight - finalHeight) / 2;

            pdf.addImage(imgData, 'PNG', x, y, finalWidth, finalHeight);
            pdf.save('task-attachments-single.pdf');
        } catch (error) {
            console.error('Error generating PDF:', error);
            alert('Có lỗi xảy ra khi tạo PDF. Vui lòng thử lại.');
        }
    };

    return (
        <div>
            {/* Nút in và export PDF */}
            <div className="tw-flex tw-justify-end tw-gap-2 tw-mb-4">
                <button
                    onClick={handlePrint}
                    className="tw-flex tw-items-center tw-gap-2 tw-px-4 tw-py-2 tw-bg-blue-500 tw-text-white tw-rounded hover:tw-bg-blue-600 tw-transition-colors"
                >
                    <Printer size={16} />
                    <span>Print Document</span>
                </button>
                <button
                    onClick={handleExportPDF}
                    className="tw-flex tw-items-center tw-gap-2 tw-px-4 tw-py-2 tw-bg-red-500 tw-text-white tw-rounded hover:tw-bg-red-600 tw-transition-colors"
                    title="Export PDF as single image"
                >
                    <Download size={16} />
                    <span>Export PDF (Single)</span>
                </button>
            </div>

            {/* Nội dung cần in */}
            <div ref={contentRef} className="tw-print-container">
                <div className="tw-p-6 tw-bg-white">
                    <h2 className="tw-text-2xl tw-font-bold tw-mb-6 tw-text-center">Task Attachments</h2>
                    <TaskAttachmentsSection fileAttachments={fileAttachments} />
                </div>
            </div>

            {/* CSS cho in ấn và page breaks */}
            <style>{`
                @media print {
                    body * {
                        visibility: hidden;
                    }
                    .tw-print-container, .tw-print-container * {
                        visibility: visible;
                    }
                    .tw-print-container {
                        position: absolute;
                        left: 0;
                        top: 0;
                        width: 100%;
                    }

                    /* Page break rules */
                    .workflow-step-section {
                        page-break-inside: avoid;
                        break-inside: avoid;
                        margin-bottom: 20px;
                    }

                    .workflow-step-section:not(:first-child) {
                        page-break-before: auto;
                        break-before: auto;
                    }

                    .file-item {
                        page-break-inside: avoid;
                        break-inside: avoid;
                        margin-bottom: 8px;
                    }

                    .step-header {
                        page-break-after: avoid;
                        break-after: avoid;
                        margin-bottom: 12px;
                    }

                    /* Override oklch colors for print compatibility */
                    * {
                        color: #000000 !important;
                        background-color: #ffffff !important;
                        border-color: #cccccc !important;
                    }
                    .tw-text-gray-900, .tw-text-gray-800, .tw-text-gray-700 {
                        color: #1f2937 !important;
                    }
                    .tw-text-gray-600, .tw-text-gray-500 {
                        color: #6b7280 !important;
                    }
                    .tw-text-gray-400 {
                        color: #9ca3af !important;
                    }
                }
            `}</style>
        </div>
    );
};

export default PrintableTaskAttachments;
