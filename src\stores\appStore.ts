import type {} from '@redux-devtools/extension';
import { nanoid } from 'nanoid';
import { FormFields, FormFieldsCheckboxOption, FormItem } from 'types/Form';
import Area from 'types/OperationalArea';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export interface AppStore {
    isLoadingApp: boolean;
    setIsLoadingApp: (isLoading: boolean) => void;
    formItem: FormItem;
    setFormItem: (formItem: FormItem) => void;
    areaList: Area[];
    setAreaList: (areaList: Area[]) => void;
    activeFields: FormFields | null;
    setActiveFields: (activeFields: FormFields | null) => void;
    listOption: FormFieldsCheckboxOption[];
    setListOption: (listOption: FormFieldsCheckboxOption[]) => void;
    activeOption: FormFieldsCheckboxOption | null;
    setActiveOption: (activeOption: FormFieldsCheckboxOption | null) => void;
}

export const useAppStore = create<AppStore>()(
    devtools((set) => ({
        isLoadingApp: false,
        setIsLoadingApp: (isLoadingApp: boolean) => set({ isLoadingApp }),
        formItem: {
            form_key: nanoid(),
            form_fields: [],
            settings: {
                title: '',
                button: [],
                isReview: false,
                formAlignment: 'left',
            },
        },
        activeFields: null,
        setActiveFields: (activeFields: FormFields | null) => set({ activeFields }),
        setFormItem: (formItem: FormItem) => set({ formItem }),
        areaList: [],
        setAreaList: (areaList: Area[]) => set({ areaList }),
        listOption: [],
        setListOption: (listOption: FormFieldsCheckboxOption[]) => set({ listOption }),
        activeOption: null,
        setActiveOption: (activeOption: FormFieldsCheckboxOption | null) => set({ activeOption }),
    }))
);
