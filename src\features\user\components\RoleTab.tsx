import React from 'react';
import Role from '../../../types/Role';

interface RoleTabProps {
    activeTab: string;
    roles: Role[];
    onChangeRole: (roleId: string) => void;
}

export default function RoleTab({ activeTab, roles, onChangeRole }: RoleTabProps) {
    return (
        <ul className="nav nav-tabs" role="tablist">
            <li className="nav-item" role="presentation">
                <button
                    type="button"
                    className={`nav-link waves-effect ${activeTab === 'gatekeeper' ? 'active' : ''}`}
                    role="tab"
                    data-bs-toggle="tab"
                    data-bs-target="#navs-gatekeeper"
                    aria-controls="navs-gatekeeper"
                    aria-selected={activeTab === 'gatekeeper'}
                    onClick={() => onChangeRole('gatekeeper')}
                >
                    Gatekeeper
                </button>
            </li>
            {roles.map((role) => (
                <li className="nav-item" role="presentation" key={role.id}>
                    <button
                        className={`nav-link waves-effect ${activeTab === role.id ? 'active' : ''}`}
                        role="tab"
                        data-bs-toggle="tab"
                        data-bs-target={`#navs-${role.id}`}
                        aria-controls={`navs-${role.id}`}
                        aria-selected={activeTab === role.id}
                        onClick={() => onChangeRole(role.id)}
                    >
                        {role.name}
                    </button>
                </li>
            ))}
        </ul>
    );
}
