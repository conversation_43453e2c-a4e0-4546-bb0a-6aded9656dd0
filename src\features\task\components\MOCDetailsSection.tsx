import React from 'react';
import CollapsibleForm from './CollapsibleForm';
import PreviousFormFields from 'features/form/components/PreviousFormFields';
import { Task, TaskStatus } from '../../../types/Task';
import { FormItem } from 'types/Form';
import { UserTaskEntity } from 'types/Workflow';

interface IProps {
    userTaskDetail?: Task;
    groupedByTaskKey: Record<string, UserTaskEntity[]>;
    forExport?: boolean;
}

export default function MOCDetailsSection({ userTaskDetail, groupedByTaskKey, forExport = false }: Readonly<IProps>) {
    const isFirstForm = userTaskDetail?.workflow_instance?.workflow_definition?.workflow_forms?.find(
        (item) => item.is_first_form
    );

    const isFirstUserTaskName = userTaskDetail?.workflow_instance?.workflow_definition?.workflow_forms?.find(
        (item) => item.is_first_form
    );

    return (
        <div className="tab-content">
            <CollapsibleForm title={isFirstUserTaskName?.task_name || ''} defaultOpen={forExport}>
                <div className="tw-px-8 tw-py-[60px] pb-0">
                    <PreviousFormFields
                        initialData={userTaskDetail?.workflow_instance?.form_data}
                        status={TaskStatus.SUBMITTED}
                        createBy={isFirstForm?.form?.creator?.full_name || ''}
                        schemaForm={isFirstForm?.form?.schema as FormItem}
                        dataPlanAndAreaUnit={userTaskDetail?.workflow_instance?.form_data?.area_of_implementation}
                        assignee={isFirstForm?.created_by}
                        firstAreaOwner={userTaskDetail?.workflow_instance?.camunda_variables?.firstAreaOwner}
                        stepName={isFirstUserTaskName?.task_name}
                        isFileEntry
                    />
                </div>
            </CollapsibleForm>
            {Object.entries(groupedByTaskKey).map(([taskKey, tasks]) => {
                const taskName = tasks[0]?.task_name || taskKey;
                return (
                    <CollapsibleForm key={taskName} title={taskName} defaultOpen={forExport}>
                        <div className="tw-px-8 tw-py-[60px] pb-0 space-y-4">
                            {tasks.map((task, index) => (
                                <PreviousFormFields
                                    key={`${taskName}-${index}`}
                                    initialData={task?.form_data}
                                    status={task.status}
                                    createBy={task.assigneeInfo?.full_name}
                                    schemaForm={task?.form?.schema as FormItem}
                                    dataPlanAndAreaUnit={task?.workflow_instance?.form_data?.area_of_implementation}
                                    assignee={task?.assignee}
                                    firstAreaOwner={
                                        userTaskDetail?.workflow_instance?.camunda_variables?.firstAreaOwner
                                    }
                                    stepName={task?.task_name}
                                    isFileEntry
                                />
                            ))}
                        </div>
                    </CollapsibleForm>
                );
            })}
        </div>
    );
}
