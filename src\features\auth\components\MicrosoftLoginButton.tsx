import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { AUTH_GET_MICROSOFT_LOGIN_URL } from 'services/UserService';
import { OPERATION_NAME, QUERY_KEY } from '../../../constants/common';
import { useEffect, useState } from 'react';
import { MicrosoftLoginUrlRes } from '../../../types/User';

export default function MicrosoftLoginButton() {
    const [shouldFetch, setShouldFetch] = useState(false);

    const { data, isLoading, isError } = useGraphQLQuery<MicrosoftLoginUrlRes>(
        [QUERY_KEY.MICROSOFT_LOGIN_URL, shouldFetch],
        AUTH_GET_MICROSOFT_LOGIN_URL,
        undefined,
        OPERATION_NAME.LOGIN,
        {
            enabled: shouldFetch,
        }
    );

    useEffect(() => {
        if (data && shouldFetch) {
            if (data.auth_get_azure_login_url?.url) {
                window.location.href = data.auth_get_azure_login_url.url;
            }
            setShouldFetch(false);
        }

        if (isError && shouldFetch) {
            setShouldFetch(false);
        }
    }, [data, isError, shouldFetch]);

    const handleMicrosoftLogin = () => {
        setShouldFetch(true);
    };

    return (
        <button
            type="button"
            className="btn btn-outline-primary mb-1 d-flex align-items-center justify-content-center"
            onClick={handleMicrosoftLogin}
            disabled={isLoading}
        >
            <img src="/assets/images/pages/login/microsoft.svg" alt="microsoft" height="14" className="me-2" />
            <span className="fw-bold">{isLoading ? 'Processing...' : 'Sign in with Microsoft'}</span>
        </button>
    );
}
