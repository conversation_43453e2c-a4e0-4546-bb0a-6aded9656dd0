import { Controller, useFormContext } from 'react-hook-form';
import FileComponent from './FileComponent';

interface FileUploadProps {
    name: string;
    disabled?: boolean;
    isFileEntry?: boolean;
}

export default function FileUpload({ name, disabled, isFileEntry }: FileUploadProps) {
    const { control } = useFormContext();

    return (
        <Controller
            name={name}
            control={control}
            render={({ field }) => (
                <FileComponent field={field} disabled={disabled || false} isFileEntry={isFileEntry} />
            )}
        />
    );
}
