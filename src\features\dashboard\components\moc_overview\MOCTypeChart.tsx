import React, { useMemo } from 'react';
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';
import { OverviewTypeSummary } from '../../../../types/Dashboard';

interface IProps {
    mocTypeData: OverviewTypeSummary[];
}

const MOCTypeChart = ({ mocTypeData }: Readonly<IProps>) => {
    const defaultData = useMemo(() => ({ summary: { software: 0, facility_non_process: 0, facility_process: 0 } }), []);
    const temporaryData = useMemo(
        () => mocTypeData.find((item) => item.type === 'temporary') ?? defaultData,
        [mocTypeData, defaultData]
    );
    const permanentData = useMemo(
        () => mocTypeData.find((item) => item.type === 'permanent') ?? defaultData,
        [mocTypeData, defaultData]
    );

    // Temporary chart options
    const temporaryChartOptions: ApexOptions = {
        chart: {
            type: 'pie',
            toolbar: {
                show: false,
            },
        },
        labels: ['Software', 'Facility (Non-Process)', 'Facility (Process)'],
        colors: ['#8bc34a', '#689f38', '#33691e'], // Green shades
        legend: {
            position: 'bottom',
            fontSize: '12px',
            markers: {
                size: 12,
            },
        },
        dataLabels: {
            enabled: true,
            formatter: function (val: number) {
                return val.toFixed(1) + '%';
            },
            style: {
                fontSize: '12px',
                fontWeight: 'bold',
                colors: ['#fff'],
            },
        },
        plotOptions: {
            pie: {
                donut: {
                    size: '0%',
                },
            },
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return val.toString();
                },
            },
        },
        title: {
            text: 'Temporary',
            align: 'center',
            style: {
                fontSize: '16px',
                fontWeight: 'bold',
                color: '#373d3f',
            },
        },
    };

    const temporaryChartSeries = useMemo(
        () => [
            temporaryData?.summary.software,
            temporaryData?.summary.facility_non_process,
            temporaryData?.summary.facility_process,
        ],
        [temporaryData]
    );

    const temporaryTotal = useMemo(
        () => temporaryChartSeries.reduce((sum, value) => sum + (value || 0), 0),
        [temporaryChartSeries]
    );

    // Permanent chart options
    const permanentChartOptions: ApexOptions = {
        chart: {
            type: 'pie',
            toolbar: {
                show: false,
            },
        },
        labels: ['Software', 'Facility (Non-Process)', 'Facility (Process)'],
        colors: ['#03a9f4', '#0288d1', '#01579b'], // Blue shades
        legend: {
            position: 'bottom',
            fontSize: '12px',
            markers: {
                size: 12,
            },
        },
        dataLabels: {
            enabled: true,
            formatter: function (val: number) {
                return val.toFixed(1) + '%';
            },
            style: {
                fontSize: '12px',
                fontWeight: 'bold',
                colors: ['#fff'],
            },
        },
        plotOptions: {
            pie: {
                donut: {
                    size: '0%',
                },
            },
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return val.toString();
                },
            },
        },
        title: {
            text: 'Permanent',
            align: 'center',
            style: {
                fontSize: '16px',
                fontWeight: 'bold',
                color: '#373d3f',
            },
        },
    };

    const permanentChartSeries = useMemo(
        () => [
            permanentData?.summary.software,
            permanentData?.summary.facility_non_process,
            permanentData?.summary.facility_process,
        ],
        [permanentData]
    );

    const permanentTotal = useMemo(
        () => permanentChartSeries.reduce((sum, value) => sum + (value || 0), 0),
        [permanentChartSeries]
    );

    return (
        <div className="tw-w-full tw-grid tw-grid-cols-1 md:tw-grid-cols-2 tw-gap-6">
            {/* Main Title */}
            <div className="card">
                <div className="card-body">
                    <div className="tw-text-center tw-mb-6">
                        <h3 className="tw-text-lg tw-font-bold tw-text-gray-700">MOC Type</h3>
                    </div>

                    <div className="tw-grid tw-grid-cols-1 md:tw-grid-cols-2 tw-gap-6">
                        {/* Temporary Chart */}
                        <div className="tw-relative">
                            {/* Total badge for Temporary */}
                            {temporaryTotal > 0 && (
                                <div className="tw-absolute tw-top-4 tw-left-4 tw-bg-green-100 tw-border tw-border-green-300 tw-px-3 tw-py-1 tw-rounded tw-z-10">
                                    <span className="tw-text-sm tw-font-semibold tw-text-green-700">
                                        Total: {temporaryTotal}
                                    </span>
                                </div>
                            )}

                            {temporaryTotal > 0 ? (
                                <Chart
                                    options={temporaryChartOptions}
                                    series={temporaryChartSeries}
                                    type="pie"
                                    height={350}
                                    key={`temporary-${temporaryTotal}-${temporaryChartSeries.join('-')}`}
                                />
                            ) : (
                                <div className="tw-h-[350px] tw-text-center tw-pt-1">
                                    <p className="tw-text-black tw-font-bold tw-text-lg">Temporary</p>
                                    <div className="tw-flex tw-flex-col tw-items-center tw-justify-center tw-h-[80%]">
                                        <div className="tw-text-center">
                                            <div className="tw-text-gray-400 tw-text-lg tw-mb-2">📊</div>
                                            <p className="tw-text-gray-500 tw-text-sm">No data available</p>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Permanent Chart */}
                        <div className="tw-relative">
                            {/* Total badge for Permanent */}
                            {permanentTotal > 0 && (
                                <div className="tw-absolute tw-top-4 tw-left-4 tw-bg-blue-100 tw-border tw-border-blue-300 tw-px-3 tw-py-1 tw-rounded tw-z-10">
                                    <span className="tw-text-sm tw-font-semibold tw-text-blue-700">
                                        Total: {permanentTotal}
                                    </span>
                                </div>
                            )}

                            {permanentTotal > 0 ? (
                                <Chart
                                    options={permanentChartOptions}
                                    series={permanentChartSeries}
                                    type="pie"
                                    height={350}
                                    key={`permanent-${permanentTotal}-${permanentChartSeries.join('-')}`}
                                />
                            ) : (
                                <div className="tw-h-[350px] tw-text-center tw-pt-1">
                                    <p className="tw-text-black tw-font-bold tw-text-lg">Permanent</p>
                                    <div className="tw-flex tw-flex-col tw-items-center tw-justify-center tw-h-[80%]">
                                        <div className="tw-text-center">
                                            <div className="tw-text-gray-400 tw-text-lg tw-mb-2">📊</div>
                                            <p className="tw-text-gray-500 tw-text-sm">No data available</p>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default MOCTypeChart;
