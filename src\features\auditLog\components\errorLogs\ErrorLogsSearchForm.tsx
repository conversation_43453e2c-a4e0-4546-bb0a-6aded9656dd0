import SearchForm from '../../../../components/partials/SearchForm';
import { errorCategoryNames, SystemNames } from '../../../../types/Logs';

interface IProps {
    isLoading: boolean;
    isRefetching: boolean;
}

export default function ErrorLogsSearchForm({ isLoading, isRefetching }: Readonly<IProps>) {
    return (
        <SearchForm
            fields={[
                {
                    name: 'error_message',
                    type: 'text',
                    label: 'Keyword',
                    wrapClassName: 'col-md-3 col-12',
                    placeholder: 'Search error message',
                },
                {
                    name: 'created_at__range',
                    type: 'date-range',
                    label: 'Timestamp Range',
                    wrapClassName: 'col-md-3 col-12',
                },
                {
                    name: 'error_category',
                    type: 'select',
                    label: 'Error Category',
                    wrapClassName: 'col-md-3 col-12',
                    options: {
                        multiple: true,
                        choices: errorCategoryNames.map((item) => ({
                            label: item.name,
                            value: item.id,
                        })),
                    },
                },
                {
                    name: 'system',
                    type: 'select',
                    label: 'System',
                    wrapClassName: 'col-md-3 col-12',
                    options: {
                        multiple: true,
                        choices: SystemNames.map((item) => ({
                            label: item.name,
                            value: item.id,
                        })),
                    },
                },
            ]}
            isLoading={isLoading || isRefetching}
        />
    );
}
