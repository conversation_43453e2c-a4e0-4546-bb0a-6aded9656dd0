/* eslint-disable react-hooks/exhaustive-deps */
import ContentHeader from 'components/partials/ContentHeader';
import PaginationTable from 'components/partials/PaginationTable';
import Spinner from 'components/partials/Spinner';
import { PAGINATION, QUERY_KEY } from 'constants/common';
import useQueryParams from 'hooks/useQueryParams';
import isUndefined from 'lodash/isUndefined';
import omitBy from 'lodash/omitBy';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { generateFilters } from 'utils/common';
import ListTask from '../components/ListTask';
import SearchTaskForm from '../components/SearchTaskForm';
import { SearchTaskParam, TaskQuery, taskFilterConfig, TaskStatus } from '../../../types/Task';
import { TASK_LIST } from 'services/TaskService';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { keepPreviousData } from '@tanstack/react-query';
import { convertDateRangeToQueryParams } from '../../../utils/date';
import { useEffect } from 'react';

export default function TaskList() {
    const { t } = useTranslation();

    const { queryParams, setQueryParams } = useQueryParams<SearchTaskParam>();

    useEffect(() => {
        if (!queryParams.status) {
            setQueryParams({
                ...queryParams,
                status: TaskStatus.IN_PROGRESS,
            });
        }
    }, []);

    const paramConfig: SearchTaskParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search: queryParams.search,
            status: queryParams.status,
            assignee: queryParams.assignee,
            tracking_status: queryParams.tracking_status,
            created_at__from:
                convertDateRangeToQueryParams(queryParams.created_at__range || '')?.created_at_from ?? undefined,
            created_at__to:
                convertDateRangeToQueryParams(queryParams.created_at__range || '')?.created_at_to ?? undefined,
        },
        isUndefined
    );

    const { search, limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, taskFilterConfig);

    const { data, isLoading, isRefetching } = useGraphQLQuery<TaskQuery>(
        [QUERY_KEY.USER_TASKS, queryParams],
        TASK_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            search: search || undefined,
            filters: filters.length > 0 ? filters : undefined,
            sort: undefined,
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    const tasks = data?.user_tasks_by_assignee?.data || [];
    const totalCount = data?.user_tasks_by_assignee?.totalCount || 0;

    const paging = {
        count_item: totalCount,
        total_page: Math.ceil(totalCount / Number(limit)),
        current_page: Number(page),
        limit: Number(limit),
    };

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({
            ...paramConfig,
            page: page.toString(),
        });
    };

    return (
        <>
            <Helmet>
                <title>{t('Task')}</title>
            </Helmet>
            <ContentHeader title={t('Task')} />
            <div className="content-body">
                <div className="col-12">
                    <SearchTaskForm isLoading={isLoading || isRefetching} />

                    {isLoading || isRefetching ? (
                        <Spinner />
                    ) : (
                        <div className="card">
                            <ListTask items={tasks} />
                            <PaginationTable
                                countItem={totalCount}
                                totalPage={paging.total_page}
                                currentPage={paging.current_page}
                                handlePageChange={handlePageChange}
                            />
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}
