import SearchForm from '../../../components/partials/SearchForm';
import Area from '../../../types/OperationalArea';
import { AuthGroupNames } from '../../../types/User';
import Role from '../../../types/Role';
interface IProps {
    isLoading: boolean;
    isRefetching: boolean;
    subAreaList: Area[];
    roleList: Role[];
}

export default function SearchUserForm({ isLoading, isRefetching, subAreaList, roleList }: Readonly<IProps>) {
    return (
        <SearchForm
            fields={[
                {
                    name: 'search',
                    type: 'text',
                    label: 'Search',
                    wrapClassName: 'col-md-3 col-12',
                    placeholder: 'Search by Full name, email',
                },
                {
                    name: 'userAreas.area_id',
                    type: 'select',
                    label: 'Sub Area',
                    wrapClassName: 'col-md-3 col-12',
                    options: {
                        multiple: true,
                        choices: subAreaList.map((area) => ({
                            label: area.name,
                            value: area.id,
                        })),
                    },
                },
                {
                    name: 'auth_group',
                    type: 'select',
                    label: 'Authorize Group',
                    wrapClassName: 'col-md-3 col-12',
                    options: {
                        multiple: true,
                        choices: AuthGroupNames.map((group) => ({
                            label: group.name,
                            value: group.id,
                        })),
                    },
                },
                {
                    name: 'userAreaRoles.role_id',
                    type: 'select',
                    label: 'Approval Role',
                    wrapClassName: 'col-md-3 col-12',
                    options: {
                        multiple: true,
                        choices: roleList.map((role) => ({
                            label: role.name,
                            value: role.id!,
                        })),
                    },
                },
            ]}
            isLoading={isLoading || isRefetching}
        />
    );
}
