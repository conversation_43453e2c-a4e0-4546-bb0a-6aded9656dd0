import { TaskDashboard } from '../../../types/Task';
import { TrackingStatus } from '../../../types/Workflow';

interface IProps {
    taskDashboard?: TaskDashboard;
    onChangeTrackingStatus: (trackingStatus: TrackingStatus) => void;
}

export default function TaskStatistics({ taskDashboard, onChangeTrackingStatus }: Readonly<IProps>) {
    return (
        <div
            className="d-grid"
            style={{
                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                gap: '1rem',
            }}
        >
            <div className="card">
                <button
                    type="button"
                    className="card-header tw-w-full tw-text-left"
                    onClick={() => onChangeTrackingStatus(TrackingStatus.ON_TRACKING)}
                >
                    <div>
                        <h2 className="fw-bolder mb-0">{taskDashboard?.in_progress}</h2>
                        <p className="card-text">On Tracking</p>
                    </div>
                    <div className="avatar bg-light-warning p-50 m-0">
                        <div className="avatar-content">
                            <i className="font-medium-5 bi bi-list-task"></i>
                        </div>
                    </div>
                </button>
            </div>
            <div className="card">
                <button
                    type="button"
                    className="card-header tw-w-full tw-text-left"
                    onClick={() => onChangeTrackingStatus(TrackingStatus.COMPLETED)}
                >
                    <div>
                        <h2 className="fw-bolder mb-0">{taskDashboard?.completed}</h2>
                        <p className="card-text">Completed</p>
                    </div>
                    <div className="avatar bg-light-success p-50 m-0">
                        <div className="avatar-content">
                            <i className="font-medium-5 bi bi-hourglass-split"></i>
                        </div>
                    </div>
                </button>
            </div>
            <div className="card">
                <button
                    type="button"
                    className="card-header tw-w-full tw-text-left"
                    onClick={() => onChangeTrackingStatus(TrackingStatus.CANCELLED)}
                >
                    <div>
                        <h2 className="fw-bolder mb-0">{taskDashboard?.cancelled}</h2>
                        <p className="card-text">Cancelled</p>
                    </div>
                    <div className="avatar bg-light-default p-50 m-0">
                        <div className="avatar-content">
                            <i className="font-medium-5 bi bi-check-circle"></i>
                        </div>
                    </div>
                </button>
            </div>
            <div className="card">
                <button
                    type="button"
                    className="card-header tw-w-full tw-text-left"
                    onClick={() => onChangeTrackingStatus(TrackingStatus.OVERDUE)}
                >
                    <div>
                        <h2 className="fw-bolder mb-0">{taskDashboard?.overdue}</h2>
                        <p className="card-text">Overdue</p>
                    </div>
                    <div className="avatar bg-light-warning p-50 m-0">
                        <div className="avatar-content">
                            <i className="font-medium-5 bi bi-check-circle"></i>
                        </div>
                    </div>
                </button>
            </div>
            <div className="card">
                <button
                    type="button"
                    className="card-header tw-w-full tw-text-left"
                    onClick={() => onChangeTrackingStatus(TrackingStatus.BACKLOG)}
                >
                    <div>
                        <h2 className="fw-bolder mb-0">{taskDashboard?.backlog}</h2>
                        <p className="card-text">Backlog</p>
                    </div>
                    <div className="avatar bg-light-primary p-50 m-0">
                        <div className="avatar-content">
                            <i className="font-medium-5 bi bi-check-circle"></i>
                        </div>
                    </div>
                </button>
            </div>
        </div>
    );
}
