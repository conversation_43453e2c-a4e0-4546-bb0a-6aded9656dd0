import classNames from 'classnames';
import { useLayoutEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { toggleModalOpen } from 'utils/common';

interface IProps {
    show: boolean;
    text: string;
    btnDisabled: boolean;
    changeShow: (s: boolean) => void;
    submitAction: (reason: string) => void;
    textTitle?: string;
    actionType: 'terminate' | 'reject';
    isLoading?: boolean;
}

export default function ModalReasonConfirm({
    show,
    text,
    btnDisabled,
    changeShow,
    submitAction,
    textTitle,
    actionType,
    isLoading = false,
}: Readonly<IProps>) {
    useLayoutEffect(() => toggleModalOpen(show), [show]);
    const { t } = useTranslation();
    const [reason, setReason] = useState('');
    const [reasonError, setReasonError] = useState('');

    const handleSubmit = () => {
        if (!reason.trim()) {
            setReasonError('Reason is required');
            return;
        }
        setReasonError('');
        submitAction(reason.trim());
    };

    const handleClose = () => {
        setReason('');
        setReasonError('');
        changeShow(false);
    };

    const getButtonText = () => {
        if (actionType === 'terminate') return 'Terminate';
        if (actionType === 'reject') return 'Reject';
        return 'Confirm';
    };

    const getButtonClass = () => {
        if (actionType === 'terminate') return 'btn-warning';
        if (actionType === 'reject') return 'btn-danger';
        return 'btn-primary';
    };

    return (
        <>
            <div
                className={classNames('modal fade text-start', {
                    show,
                    'modal-danger': actionType === 'reject',
                    'modal-warning': actionType === 'terminate',
                })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">{textTitle}</h5>
                            <button type="button" className="btn-close" onClick={handleClose} />
                        </div>
                        <div className="modal-body">
                            <p className="tw-mb-3">{text}</p>
                            <div className="tw-mb-3">
                                <label className="form-label tw-font-semibold">
                                    Reason <span className="tw-text-red-500">*</span>
                                </label>
                                <textarea
                                    className={classNames('form-control', {
                                        'is-invalid': reasonError,
                                    })}
                                    rows={4}
                                    placeholder={`Please provide a reason for ${actionType}...`}
                                    value={reason}
                                    onChange={(e) => {
                                        setReason(e.target.value);
                                        if (reasonError) setReasonError('');
                                    }}
                                    maxLength={2000}
                                />
                                {reasonError && <div className="invalid-feedback">{reasonError}</div>}
                            </div>
                        </div>
                        <div className="modal-footer">
                            <button
                                type="button"
                                className="btn btn-secondary"
                                onClick={handleClose}
                                disabled={isLoading}
                            >
                                Cancel
                            </button>
                            <button
                                type="button"
                                className={classNames('btn', getButtonClass())}
                                disabled={btnDisabled || isLoading || !reason.trim()}
                                onClick={handleSubmit}
                            >
                                {isLoading ? (
                                    <>
                                        <span
                                            className="spinner-border spinner-border-sm me-2"
                                            role="status"
                                            aria-hidden="true"
                                        ></span>
                                        Processing...
                                    </>
                                ) : (
                                    getButtonText()
                                )}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show"></div>}
        </>
    );
}
