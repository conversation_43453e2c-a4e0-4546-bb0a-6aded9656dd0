import { useState, useEffect, useMemo } from 'react';
import { Folder, Search } from 'react-feather';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { CREATE_USER, OMS_GET_EMPLOYEE_INFO } from '../../../services/UserService';
import { AREAS_LIST } from '../../../services/AreaService';
import { AuthGroupNames, AuthGroups, OmsEmployeeInfoRes, OmsEmployeeInfo } from '../../../types/User';
import Area, { AreaType, AreaQueryRes } from '../../../types/OperationalArea';
import { showToast } from '../../../utils/common';
import { QUERY_KEY } from '../../../constants/common';

interface UserCreateInputDto {
    user_name: string;
    auth_group: AuthGroups;
    area_ids: string[];
}

interface AddUserFormProps {
    onClose?: () => void;
    onSuccess?: () => void;
    refetchUserList?: () => void;
    isOpen?: boolean;
}

export default function AddUserForm({ onClose, onSuccess, refetchUserList, isOpen }: Readonly<AddUserFormProps>) {
    const [searchValue, setSearchValue] = useState('');
    const [selectedUser, setSelectedUser] = useState<OmsEmployeeInfo | null>(null);
    const [selectedAuthGroup, setSelectedAuthGroup] = useState<AuthGroups | null>(null);
    const [selectedAreas, setSelectedAreas] = useState<string[]>([]);
    const [areaSearchTerm, setAreaSearchTerm] = useState('');
    const [searchUsers, setSearchUsers] = useState<OmsEmployeeInfo[]>([]);
    const [showNoUserMessage, setShowNoUserMessage] = useState(false);

    const { data: areasData } = useGraphQLQuery<AreaQueryRes>([QUERY_KEY.AREAS, AreaType.SUB_AREA], AREAS_LIST, {
        filters: [`type:=(${AreaType.SUB_AREA})`],
    });

    const subAreas: Area[] = areasData?.areas_list || [];

    const filteredAreas = subAreas.filter((area) => area.name.toLowerCase().includes(areaSearchTerm.toLowerCase()));

    const { mutate: searchUserMutate, isPending: isSearchingUser } = useGraphQLMutation<
        OmsEmployeeInfoRes,
        { username: string }
    >(OMS_GET_EMPLOYEE_INFO, '', {
        onSuccess: (data) => {
            const users = data.oms_get_employee_info.responseData;
            setSearchUsers(users);
            setShowNoUserMessage(false);
        },
        onError: () => {
            setSearchUsers([]);
            setShowNoUserMessage(true);
        },
    });

    const createUserMutation = useGraphQLMutation<{ create_user: { id: string } }, { body: UserCreateInputDto }>(
        CREATE_USER,
        '',
        {
            onSuccess: () => {
                showToast(true, ['Add user success']);
                resetForm();
                if (onSuccess) onSuccess();
                if (onClose) onClose();
                if (refetchUserList) refetchUserList();
            },
        }
    );

    const handleFindUser = async () => {
        if (!searchValue.trim()) {
            showToast(false, ['Please enter search value']);
            return;
        }
        setSelectedUser(null);
        searchUserMutate({ username: searchValue });
    };

    const handleUserSelect = (user: OmsEmployeeInfo) => {
        setSelectedUser(user);
    };

    const handleAuthGroupChange = (groupId: AuthGroups) => {
        setSelectedAuthGroup(groupId);
    };

    const handleAreaToggle = (areaId: string) => {
        setSelectedAreas([areaId]);
    };

    const resetForm = () => {
        setSearchValue('');
        setSelectedUser(null);
        setSelectedAuthGroup(null);
        setSelectedAreas([]);
        setAreaSearchTerm('');
        setSearchUsers([]);
        setShowNoUserMessage(false);
    };

    const handleCancel = () => {
        resetForm();
        if (onClose) onClose();
    };

    const handleAddUser = () => {
        if (!selectedUser || !selectedAuthGroup) {
            showToast(false, ['Please select user or authorize group']);
            return;
        }

        if (
            (selectedAuthGroup === AuthGroups.MOC_USER || selectedAuthGroup === AuthGroups.MOC_MANAGER) &&
            !selectedAreas.length
        ) {
            showToast(false, ['Please select sub area']);
            return;
        }

        const createData: UserCreateInputDto = {
            user_name: selectedUser.adAcount,
            auth_group: selectedAuthGroup,
            area_ids: selectedAreas,
        };
        if (selectedAuthGroup === AuthGroups.ADMIN || selectedAuthGroup === AuthGroups.SUPER_ADMIN) {
            createData.area_ids = [];
        }
        createUserMutation.mutate({ body: createData });
    };

    const isFormValid = useMemo(() => {
        if (selectedAuthGroup === AuthGroups.ADMIN || selectedAuthGroup === AuthGroups.SUPER_ADMIN) {
            return selectedUser && selectedAuthGroup;
        }
        return selectedUser && selectedAuthGroup && selectedAreas.length > 0;
    }, [selectedUser, selectedAuthGroup, selectedAreas]);

    useEffect(() => {
        if (!isOpen) {
            resetForm();
        }
    }, [isOpen]);

    return (
        <div>
            <div className="mb-3">
                <label className="form-label fs-5 fw-bold">Input user name to search user from OMS</label>
                <div className="input-group" style={{ maxWidth: '400px' }}>
                    <input
                        type="text"
                        className="form-control"
                        placeholder="User name"
                        value={searchValue}
                        onChange={(e) => setSearchValue(e.target.value)}
                        onKeyUp={(e) => e.key === 'Enter' && handleFindUser()}
                    />
                    <button
                        className="btn btn-primary"
                        type="button"
                        onClick={handleFindUser}
                        disabled={isSearchingUser}
                    >
                        {isSearchingUser ? 'Searching...' : 'Find user'}
                    </button>
                </div>
            </div>

            <div className="mb-3">
                <div className="table-responsive">
                    <table className="table table-bordered">
                        <thead className="table-light">
                            <tr>
                                <th></th>
                                <th>Employee Code</th>
                                <th>Organization</th>
                                <th>Full Name</th>
                                <th>Position</th>
                                <th>Email</th>
                            </tr>
                        </thead>
                        <tbody>
                            {showNoUserMessage && (
                                <tr>
                                    <td colSpan={6} className="text-center">
                                        No user found
                                    </td>
                                </tr>
                            )}
                            {searchUsers.map((user) => (
                                <tr key={user.employeeId}>
                                    <td className="text-center">
                                        <input
                                            type="radio"
                                            name="selectedUser"
                                            className="form-check-input"
                                            checked={selectedUser?.employeeId === user.employeeId}
                                            onChange={() => handleUserSelect(user)}
                                        />
                                    </td>
                                    <td>{user.empCode}</td>
                                    <td>{user.organizationName}</td>
                                    <td>{user.t_FullName}</td>
                                    <td>{user.positionName}</td>
                                    <td>{user.email}</td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>

            {searchUsers.length > 0 && (
                <>
                    <div className="mb-3">
                        <label className="form-label">Select authorize group to assign user</label>
                        <div className="d-flex gap-3">
                            {AuthGroupNames.map((group) => (
                                <div key={group.id} className="form-check">
                                    <input
                                        className="form-check-input"
                                        type="radio"
                                        name="authGroup"
                                        id={`auth-${group.id}`}
                                        checked={selectedAuthGroup === group.id}
                                        onChange={() => handleAuthGroupChange(group.id as AuthGroups)}
                                    />
                                    <label className="form-check-label" htmlFor={`auth-${group.id}`}>
                                        {group.name}
                                    </label>
                                </div>
                            ))}
                        </div>
                    </div>

                    {(selectedAuthGroup === AuthGroups.MOC_USER || selectedAuthGroup === AuthGroups.MOC_MANAGER) && (
                        <div className="mb-3">
                            <label className="form-label">Select sub area to assign user</label>
                            <div className="mb-2">
                                <div className="input-group" style={{ maxWidth: '300px' }}>
                                    <span className="input-group-text">
                                        <Search size={16} />
                                    </span>
                                    <input
                                        type="text"
                                        className="form-control"
                                        placeholder="search sub area"
                                        value={areaSearchTerm}
                                        onChange={(e) => setAreaSearchTerm(e.target.value)}
                                    />
                                </div>
                            </div>
                            <div
                                style={{
                                    maxHeight: '200px',
                                    overflowY: 'auto',
                                    border: '1px solid #dee2e6',
                                    borderRadius: '0.375rem',
                                    padding: '0.5rem',
                                }}
                            >
                                {filteredAreas.map((area) => (
                                    <div key={area.id} className="form-check d-flex align-items-center mb-1">
                                        {/* <input
                                        className="form-check-input me-2"
                                        type="checkbox"
                                        id={`area-${area.id}`}
                                        checked={selectedAreas.includes(String(area.id!))}
                                        onChange={() => handleAreaToggle(String(area.id!))}
                                    /> */}
                                        <input
                                            type="radio"
                                            id={`area-${area.id}`}
                                            name="selectedArea"
                                            className="form-check-input me-2"
                                            checked={selectedAreas.includes(String(area.id!))}
                                            onChange={() => handleAreaToggle(String(area.id!))}
                                        />
                                        <span className="me-1">
                                            <Folder size={16} />
                                        </span>
                                        <label className="form-check-label" htmlFor={`area-${area.id}`}>
                                            {area.name} {area.code && `(${area.code})`}
                                        </label>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </>
            )}

            <div className="d-flex justify-content-end gap-2">
                <button type="button" className="btn btn-secondary" onClick={handleCancel}>
                    Cancel
                </button>
                <button
                    type="button"
                    className="btn btn-primary"
                    disabled={!isFormValid || createUserMutation.isPending}
                    onClick={handleAddUser}
                >
                    {createUserMutation.isPending ? 'Adding...' : 'Add User'}
                </button>
            </div>
        </div>
    );
}
