import { gql } from 'graphql-request';

export const ACTIVE_WORKFLOW_DETAIL = gql`
    query Active_workflow_detail {
        active_workflow_detail {
            created_by
            updated_by
            created_at
            updated_at
            deleted_at
            name
            bpmnXml
            status
            camunda_key
            camunda_id
            version
            first_task_form_id
            id
            workflow_steps {
                id
                step_key
                step_name
            }
            workflow_instances {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at

                process_instance_id
                camunda_key
                business_key
                name
                status
                started_at
                ended_at
                form_data
                camunda_variables
                current_user_task_id
            }
            forms {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                name
                schema
                description
                status
            }
        }
    }
`;
