import { useEffect, useMemo, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useGraphQLMutation } from 'hooks/useGraphQLMutation';
import { AUTH_LOGIN_WITH_AZURE_CODE } from 'services/UserService';
import { OPERATION_NAME } from '../../../constants/common';
import { showToast } from 'utils/common';
import { useAuthStore } from 'stores/authStore';
import { AuthGroups, MicrosoftLoginRes } from '../../../types/User';
import './microsoftCallback.css';
import { Helmet } from 'react-helmet-async';

export default function MicrosoftCallback(): JSX.Element {
    const [searchParams] = useSearchParams();
    const navigate = useNavigate();
    const authorized = useAuthStore((state) => state.authorized);
    const [isProcessing, setIsProcessing] = useState(true);

    const code = useMemo(() => searchParams.get('code'), [searchParams]);

    const { mutate, isError } = useGraphQLMutation<MicrosoftLoginRes>(
        AUTH_LOGIN_WITH_AZURE_CODE,
        OPERATION_NAME.MICROSOFT_LOGIN_WITH_CODE,
        {
            onSuccess: (data) => {
                if (data?.auth_login_with_azure_code) {
                    authorized(data.auth_login_with_azure_code.user);
                    showToast(true, ['Login successfully']);
                    if (data.auth_login_with_azure_code.user.auth_group === AuthGroups.MOC_USER) {
                        navigate('/changeRequest');
                    } else {
                        navigate('/moc-overview');
                    }
                } else {
                    showToast(false, ['Login failed']);
                    navigate('/');
                }
                setIsProcessing(false);
            },
            onError: () => {
                navigate('/');
                setIsProcessing(false);
            },
            retry: 0,
        }
    );

    useEffect(() => {
        if (!code) {
            navigate('/not-found');
            return;
        }

        mutate({ code });
    }, [code, mutate, navigate]);

    return (
        <>
            <Helmet>
                <title>Processing...</title>
            </Helmet>
            <div className="app-content">
                <div className="content-overlay" />
                <div className="header-navbar-shadow" />
                <div className="content-wrapper">
                    <div className="content-body">
                        <div className="auth-wrapper auth-basic">
                            <div className="auth-inner min-vh-100 d-flex align-items-center justify-content-center">
                                <div className="loading-container">
                                    <div
                                        className="spinner-border text-primary"
                                        style={{ width: '3rem', height: '3rem' }}
                                        role="status"
                                    >
                                        <span className="visually-hidden">Processing...</span>
                                    </div>
                                    <h3 className="loading-title mt-3">
                                        {isProcessing
                                            ? 'Processing login...'
                                            : isError
                                            ? 'Login failed'
                                            : 'Login successfully'}
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}
