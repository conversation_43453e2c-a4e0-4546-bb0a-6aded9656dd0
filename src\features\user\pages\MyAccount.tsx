import React from 'react';
import { Helmet } from 'react-helmet-async';
import ContentHeader from 'components/partials/ContentHeader';
import GeneralInformation from '../components/GeneralInformation';
import OperationalAreasInformation from '../components/OperationalAreasInformation';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { ProfileQuery } from '../../../types/User';
import { OPERATION_NAME, QUERY_KEY } from '../../../constants/common';
import { PROFILE } from '../../../services/UserService';
import { keepPreviousData } from '@tanstack/react-query';
import Spinner from '../../../components/partials/Spinner';

export default function MyAccount() {
    const { data: profile, isLoading: isLoadingProfile } = useGraphQLQuery<ProfileQuery>(
        [QUERY_KEY.PROFILE],
        PROFILE,
        undefined,
        OPERATION_NAME.PROFILE,
        {
            placeholderData: keepPreviousData,
        }
    );
    return (
        <>
            <Helmet>
                <title>My Account</title>
            </Helmet>
            <ContentHeader title="My Account" />
            <div className="content-body">
                {isLoadingProfile || !profile ? (
                    <Spinner />
                ) : (
                    <div className="container-fluid">
                        <div className="row">
                            <div className="col-12 mb-3">
                                <GeneralInformation userInfor={profile.auth_profile} />
                            </div>
                            <div className="col-12">
                                <OperationalAreasInformation user={profile.auth_profile} />
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </>
    );
}
