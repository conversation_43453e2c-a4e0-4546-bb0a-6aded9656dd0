import { useMemo } from 'react';
import ReactQuill, { Quill } from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import './ReactQuill.css';

// Đăng ký custom font sizes
const Size = Quill.import('attributors/style/size');
Size.whitelist = ['8px', '10px', '12px', '14px', '16px', '18px', '20px', '24px', '32px', '48px'];
Quill.register(Size, true);

interface ReactQuillEditorProps {
    value?: string;
    onChange?: (content: string) => void;
    placeholder?: string;
    height?: string;
}

export default function ReactQuillEditor({
    value,
    onChange,
    placeholder,
    height = '300px',
}: Readonly<ReactQuillEditorProps>) {
    // Cấu hình modules cho ReactQuill
    const modules = useMemo(() => {
        // Color palette tương tự như TinyEditor
        const colors = [
            '#000000',
            '#434343',
            '#666666',
            '#999999',
            '#b7b7b7',
            '#cccccc',
            '#d9d9d9',
            '#efefef',
            '#f3f3f3',
            '#ffffff',
            '#980000',
            '#ff0000',
            '#ff9900',
            '#ffff00',
            '#00ff00',
            '#00ffff',
            '#4a86e8',
            '#0000ff',
            '#9900ff',
            '#ff00ff',
            '#e6b8af',
            '#f4cccc',
            '#fce5cd',
            '#fff2cc',
            '#d9ead3',
            '#d0e0e3',
            '#c9daf8',
            '#cfe2f3',
            '#d9d2e9',
            '#ead1dc',
            '#dd7e6b',
            '#ea9999',
            '#f9cb9c',
            '#ffe599',
            '#b6d7a8',
            '#a2c4c9',
            '#a4c2f4',
            '#9fc5e8',
            '#b4a7d6',
            '#d5a6bd',
            '#cc4125',
            '#e06666',
            '#f6b26b',
            '#ffd966',
            '#93c47d',
            '#76a5af',
            '#6d9eeb',
            '#6fa8dc',
            '#8e7cc3',
            '#c27ba0',
            '#a61c00',
            '#cc0000',
            '#e69138',
            '#f1c232',
            '#6aa84f',
            '#45818e',
            '#3c78d8',
            '#3d85c6',
            '#674ea7',
            '#a64d79',
            '#85200c',
            '#990000',
            '#b45f06',
            '#bf9000',
            '#38761d',
            '#134f5c',
            '#1155cc',
            '#0b5394',
            '#351c75',
            '#741b47',
            '#5b0f00',
            '#660000',
            '#783f04',
            '#7f6000',
            '#274e13',
            '#0c343d',
            '#1c4587',
            '#073763',
            '#20124d',
            '#4c1130',
        ];

        return {
            toolbar: [
                [{ size: ['8px', '10px', '12px', '14px', '16px', '18px', '20px', '24px', '32px', '48px'] }],
                ['bold', 'italic', 'underline'],
                [{ align: [] }],
                [{ list: 'ordered' }, { list: 'bullet' }],
                [{ color: colors }, { background: colors }],
                ['clean'],
            ],
        };
    }, []);

    // Cấu hình formats
    const formats = ['size', 'bold', 'italic', 'underline', 'align', 'list', 'bullet', 'color', 'background'];

    const handleChange = (content: string) => {
        onChange && onChange(content);
    };

    return (
        <div className="react-quill-editor-container" style={{ height }}>
            <ReactQuill
                value={value || ''}
                onChange={handleChange}
                placeholder={placeholder}
                modules={modules}
                formats={formats}
                style={{ height: `calc(${height} - 66px)` }}
            />
        </div>
    );
}
