import { keepPreviousData } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import PaginationTable from 'components/partials/PaginationTable';
import Spinner from 'components/partials/Spinner';
import { PAGINATION, QUERY_KEY } from 'constants/common';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { useMemo, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import {
    DELETE_PROCESS,
    WORKFLOW_LIST,
    ACTIVE_WORKFLOW_DEFINITION,
    INACTIVATE_WORKFLOW_DEFINITION,
    REACTIVATE_WORKFLOW_DEFINITION,
} from 'services/WorkflowService';
import { useAuthStore } from 'stores/authStore';
import {
    SearchWorkflowParam,
    WorkflowListQueryVariables,
    WorkflowQuery,
    workflowFilterConfig,
    WorkflowStatus,
    WorkflowDefinition,
} from 'types/Workflow';
import ListWorkflow from '../components/ListWorkflow';
import ModalConfirm from 'components/partials/ModalConfirm';
import { useGraphQLMutation } from 'hooks/useGraphQLMutation';
import { generateFilters, showToast } from 'utils/common';
import useQueryParams from 'hooks/useQueryParams';
import { isUndefined, omitBy } from 'lodash';
import SearchWorkflowForm from '../components/SearchWorkflowForm';
import { useAppStore } from 'stores/appStore';
import { AuthGroups } from '../../../types/User';

export default function WorkflowList() {
    const { t } = useTranslation();
    const user = useAuthStore((state) => state.user);
    const [showDelete, setShowDelete] = useState(false);
    const [showActive, setShowActive] = useState(false);
    const [showInactive, setShowInactive] = useState(false);
    const [showReactivate, setShowReactivate] = useState(false);
    const [selectedId, setSelectedId] = useState<string>('');
    const [workflowActive, setWorkflowActive] = useState<WorkflowDefinition>();
    const isSuperAdmin = useMemo(() => user?.auth_group === AuthGroups.SUPER_ADMIN, [user?.auth_group]);
    const isAdmin = useMemo(() => user?.auth_group === AuthGroups.ADMIN, [user?.auth_group]);

    const { queryParams, setQueryParams } = useQueryParams<SearchWorkflowParam>();

    const paramConfig: SearchWorkflowParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? 1,
            version: queryParams.version,
            status: queryParams.status,
            search: queryParams.search,
        },
        isUndefined
    );

    const setIsLoadingApp = useAppStore((state) => state.setIsLoadingApp);
    const isLoadingApp = useAppStore((state) => state.isLoadingApp);

    const { limit, search, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, workflowFilterConfig);

    const { data, isLoading, isRefetching, refetch } = useGraphQLQuery<WorkflowQuery, WorkflowListQueryVariables>(
        [QUERY_KEY.GET_ALL_WORKFLOW, queryParams],
        WORKFLOW_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            sort: '',
            search: search,
            filters: filters.length > 0 ? filters : [],
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );
    const workflowVersions = useMemo(() => data?.workflow_definition_list_paginate?.data ?? undefined, [data]);

    const handlePageChange = (_event: React.ChangeEvent<unknown>, newPage: number) => {
        setQueryParams({
            ...paramConfig,
            page: newPage.toString(),
        });
    };

    const [deleteId, setDeleteId] = useState(0);
    const handleDelete = (item: WorkflowDefinition) => {
        setWorkflowActive(item);
        setDeleteId(item.id!);
        setShowDelete(true);
    };

    const deleteItem = async () => {
        if (!deleteId) return;
        deleteMutation.mutate({ id: deleteId });
    };

    const deleteMutation = useGraphQLMutation(DELETE_PROCESS, '', {
        onSuccess: () => {
            showToast(true, ['Workflow version deleted successfully']);
            setShowDelete(false);
            setQueryParams({
                ...paramConfig,
                page: '1',
            });
            refetch();
        },
    });

    const activeMutation = useGraphQLMutation(ACTIVE_WORKFLOW_DEFINITION, '', {
        onSuccess: () => {
            showToast(true, ['Workflow version active successfully']);
            refetch();
        },
        onSettled: () => {
            setIsLoadingApp(false);
        },
    });

    const inactivateMutation = useGraphQLMutation(INACTIVATE_WORKFLOW_DEFINITION, '', {
        onSuccess: () => {
            showToast(true, ['Workflow version inactive successfully']);
            refetch();
        },
        onSettled: () => {
            setIsLoadingApp(false);
        },
    });

    const reactivateMutation = useGraphQLMutation(REACTIVATE_WORKFLOW_DEFINITION, '', {
        onSuccess: () => {
            showToast(true, ['Reactivate workflow successfully']);
            refetch();
        },
        onSettled: () => {
            setIsLoadingApp(false);
        },
    });
    const handleChooseItem = (item: WorkflowDefinition) => {
        setWorkflowActive(item);
        const idString = item?.id?.toString() || '';
        setSelectedId(idString);
        if (item.status === WorkflowStatus.DRAFT) {
            setShowActive(true);
        } else if (item.status === WorkflowStatus.ACTIVE) {
            setShowInactive(true);
        } else if (item.status === WorkflowStatus.INACTIVE) {
            setShowReactivate(true);
        }
    };

    const handleActive = () => {
        if (!selectedId) return;
        setIsLoadingApp(true);
        activeMutation.mutate({ id: selectedId });
        setShowActive(false);
    };

    const handleInactive = () => {
        if (!selectedId) return;
        setIsLoadingApp(true);
        inactivateMutation.mutate({ id: selectedId });
        setShowInactive(false);
    };

    const handleReactivate = () => {
        if (!selectedId) return;
        reactivateMutation.mutate({ id: selectedId });
        setShowReactivate(false);
        setIsLoadingApp(true);
    };

    return (
        <>
            <Helmet>
                <title>{t('workflow.list')}</title>
            </Helmet>

            <ContentHeader
                title={t('workflow.list')}
                contextMenu={
                    workflowVersions && workflowVersions.length === 0 && (isSuperAdmin || isAdmin)
                        ? [
                              {
                                  text: t('workflow.add'),
                                  to: '/workflow/add',
                                  icon: 'PLUS',
                              },
                          ]
                        : []
                }
            />
            <div className="content-body">
                <div className="col-12">
                    <SearchWorkflowForm isLoading={isLoading || isRefetching} />

                    {(isLoading || isLoadingApp || isRefetching) && <Spinner />}
                    {!isLoading && !isLoadingApp && !isRefetching && data && (
                        <div className="card">
                            <ListWorkflow
                                items={data?.workflow_definition_list_paginate?.data}
                                paging={{
                                    count_item: data?.workflow_definition_list_paginate?.totalCount,
                                    total_page: data?.workflow_definition_list_paginate?.totalPages,
                                    current_page: data?.workflow_definition_list_paginate?.currentPage,
                                    limit: PAGINATION.limit,
                                }}
                                handleDelete={handleDelete}
                                handleChooseItem={handleChooseItem}
                                isSuperAdmin={isSuperAdmin}
                            />
                            <PaginationTable
                                countItem={data?.workflow_definition_list_paginate?.totalCount}
                                totalPage={data?.workflow_definition_list_paginate?.totalPages}
                                currentPage={data?.workflow_definition_list_paginate?.currentPage}
                                handlePageChange={handlePageChange}
                            />
                        </div>
                    )}
                </div>
                <ModalConfirm
                    show={showDelete}
                    text={`Are you sure you want to delete workflow version ${workflowActive?.version}? This action cannot be undone.`}
                    btnDisabled={false}
                    changeShow={(s: boolean) => setShowDelete(s)}
                    submitAction={deleteItem}
                    isDelete={true}
                    textTitle="Confirm Delete Workflow Version"
                />
                <ModalConfirm
                    show={showActive}
                    text={`Are you sure you want to activate workflow version ${workflowActive?.version}? This will set it as the active workflow and deactivate any previously active version.`}
                    btnDisabled={false}
                    changeShow={(s: boolean) => setShowActive(s)}
                    submitAction={handleActive}
                    textTitle="Confirm activate Workflow Version"
                />
                <ModalConfirm
                    show={showInactive}
                    text={`Are you sure you want to inactivate workflow version ${workflowActive?.version}? This will set it as the active workflow and deactivate any previously active version.`}
                    btnDisabled={false}
                    changeShow={(s: boolean) => setShowInactive(s)}
                    submitAction={handleInactive}
                    textTitle="Confirm inactivate Workflow Version"
                />
                <ModalConfirm
                    show={showReactivate}
                    text={`Are you sure you want to reactivate workflow version ${workflowActive?.version}? This will set it as the active workflow and deactivate any previously active version.`}
                    btnDisabled={false}
                    changeShow={(s: boolean) => setShowReactivate(s)}
                    submitAction={handleReactivate}
                    textTitle="Confirm reactivate Workflow Version"
                />
            </div>
        </>
    );
}
