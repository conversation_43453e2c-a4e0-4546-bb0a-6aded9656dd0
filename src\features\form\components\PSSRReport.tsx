import { Controller, Control } from 'react-hook-form';

interface PSSRPhaseTableProps {
    control: Control<any>;
    name: string;
    disabled: boolean;
}

const PSSR_PHASES = [
    'Construction Check.',
    'Installation Check.',
    'Pre-Energize Check.',
    'Pre-Commissioning Check.',
    'Final System Check*',
];

export default function PSSRPhaseTable({ control, name, disabled }: PSSRPhaseTableProps) {
    return (
        <div className="tw-overflow-auto">
            <table className="tw-w-full tw-border tw-border-collapse">
                <thead className="tw-bg-gray-100">
                    <tr>
                        <th className="tw-border tw-p-2">PSSR Phase</th>
                        <th className="tw-border tw-p-2">No. Punch "A"</th>
                        <th className="tw-border tw-p-2">No. Punch "B"</th>
                        <th className="tw-border tw-p-2">Report Date</th>
                        <th className="tw-border tw-p-2">Remark</th>
                    </tr>
                </thead>
                <tbody>
                    {PSSR_PHASES.map((phase, index) => (
                        <tr key={index}>
                            <td className="tw-border tw-p-1">{phase}</td>

                            <td className="tw-border tw-p-1">
                                <Controller
                                    name={`${name}.${index}.punchA`}
                                    control={control}
                                    render={({ field }) => (
                                        <textarea
                                            className="tw-w-full tw-outline-none tw-min-h-[40px]"
                                            {...field}
                                            disabled={disabled}
                                        />
                                    )}
                                />
                            </td>

                            <td className="tw-border tw-p-1">
                                <Controller
                                    name={`${name}.${index}.punchB`}
                                    control={control}
                                    render={({ field }) => (
                                        <textarea
                                            className="tw-w-full tw-outline-none tw-min-h-[40px]"
                                            {...field}
                                            disabled={disabled}
                                        />
                                    )}
                                />
                            </td>

                            <td className="tw-border tw-p-1">
                                <Controller
                                    name={`${name}.${index}.reportDate`}
                                    control={control}
                                    render={({ field }) => (
                                        <input
                                            type="date"
                                            className="tw-w-full tw-outline-none tw-p-1"
                                            {...field}
                                            disabled={disabled}
                                        />
                                    )}
                                />
                            </td>

                            <td className="tw-border tw-p-1">
                                <Controller
                                    name={`${name}.${index}.remark`}
                                    control={control}
                                    render={({ field }) => (
                                        <textarea
                                            className="tw-w-full tw-outline-none tw-min-h-[40px]"
                                            {...field}
                                            disabled={disabled}
                                        />
                                    )}
                                />
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
