import React, { useEffect, useMemo, useState } from 'react';
import RoleTab from '../components/RoleTab';
import { Helmet } from 'react-helmet-async';
import Role, { RoleQueryRes, RoleType } from '../../../types/Role';
import Area, { AreaType, AreaQueryRes } from '../../../types/OperationalArea';
import { keepPreviousData, useQueryClient } from '@tanstack/react-query';
import { ROLE_DETAIL, ROLES_LIST, CHANGE_USER_ROLE, REMOVE_USER_ROLE } from '../../../services/RoleService';
import { AREAS_LIST } from '../../../services/AreaService';
import { QUERY_KEY } from '../../../constants/common';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import RoleGatekeeper from '../components/RoleGatekeeper';
import RoleAreaBased from '../components/RoleAreaBased';
import RoleSystemWide from '../components/RoleSystemWide';
import ModalContent from '../../../components/partials/ModalContent';
import AssignUserForm from '../components/AssignUserForm';
import { showToast } from '../../../utils/common';
import ModalConfirm from '../../../components/partials/ModalConfirm';
import { useAuthStore } from '../../../stores/authStore';
import { AuthGroups } from '../../../types/User';

export default function RoleAssignment() {
    const queryClient = useQueryClient();
    const [activeHeaderTab, setActiveheaderTab] = useState('gatekeeper');
    const [activeGatekeeperTab, setActiveGatekeeperTab] = useState('');
    const [getkeeperRoles, setGatekeeperRoles] = useState<Role[]>([]);
    const [roleSelectedId, setRoleSelectedId] = useState('');
    const [showAssignUser, setShowAssignUser] = useState(false);
    const [selectedAreaIds, setSelectedAreaIds] = useState<string[]>([]);
    const [resetCheckboxes, setResetCheckboxes] = useState(false);
    const [showConfirmDelete, setShowConfirmDelete] = useState(false);
    const [userAreaRoleId, setUserAreaRoleId] = useState('');
    const currentUser = useAuthStore((state) => state.user);

    const { data: subAreaData } = useGraphQLQuery<AreaQueryRes>(
        [QUERY_KEY.AREAS, AreaType.SUB_AREA],
        AREAS_LIST,
        {
            filters: [`type:=(${AreaType.SUB_AREA})`],
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );
    const subAreaList: Area[] = subAreaData?.areas_list || [];

    const { data: roleData } = useGraphQLQuery<RoleQueryRes>([QUERY_KEY.ROLES], ROLES_LIST, {}, '', {
        placeholderData: keepPreviousData,
    });
    const roleList: Role[] = useMemo(() => roleData?.roles_list || [], [roleData]);

    useEffect(() => {
        const roleFilterd = roleList.filter((role) => role.is_gatekeeper_child);
        setGatekeeperRoles(roleFilterd);
        setActiveGatekeeperTab(roleFilterd[0]?.id || '');
    }, [roleList]);

    useEffect(() => {
        if (activeHeaderTab !== 'gatekeeper') {
            setRoleSelectedId(activeHeaderTab);
        } else {
            setRoleSelectedId(activeGatekeeperTab);
        }
    }, [activeHeaderTab, activeGatekeeperTab]);

    useEffect(() => {
        setRoleSelectedId(activeGatekeeperTab);
    }, [activeGatekeeperTab]);

    const { data: roleDetailData } = useGraphQLQuery<{ role_detail: Role }>(
        [QUERY_KEY.ROLE, roleSelectedId],
        ROLE_DETAIL,
        { id: roleSelectedId },
        '',
        {
            enabled: !!roleSelectedId,
            placeholderData: keepPreviousData,
        }
    );

    const roleDetail = roleDetailData?.role_detail;

    const changeUserRoleMutation = useGraphQLMutation<
        { change_user_role: boolean },
        { role_id: string; user_id: string; area_ids: string[] | null }
    >(CHANGE_USER_ROLE, '', {
        onSuccess: () => {
            showToast(true, [`User assigned successfully to role: ${roleDetail?.name}`]);
            setShowAssignUser(false);
            setSelectedAreaIds([]);
            setResetCheckboxes(true);
            setTimeout(() => setResetCheckboxes(false), 100);
            queryClient.invalidateQueries({
                queryKey: [QUERY_KEY.ROLE, roleSelectedId],
            });
        },
    });

    const handleUserSelect = (userId: string) => {
        if (roleSelectedId) {
            const isSystemWide = roleDetail?.type === RoleType.SYSTEM_WIDE;
            const canProceed = isSystemWide || selectedAreaIds.length > 0;

            if (canProceed) {
                changeUserRoleMutation.mutate({
                    role_id: roleSelectedId,
                    user_id: userId,
                    area_ids: isSystemWide ? null : selectedAreaIds,
                });
            }
        }
    };

    const handleCloseAssignUser = () => {
        setShowAssignUser(false);
        setSelectedAreaIds([]);
    };

    const handleOpenAssignUser = (areaIds: string[]) => {
        // Refetch USER_LIST_PAGINATE để lấy danh sách users mới nhất
        queryClient.invalidateQueries({
            queryKey: [QUERY_KEY.USER_ACCOUNT_LIST],
        });

        setSelectedAreaIds(areaIds);
        setShowAssignUser(true);
    };

    const handleOpenAssignUserSystemWide = () => {
        // Refetch USER_LIST_PAGINATE để lấy danh sách users mới nhất
        queryClient.invalidateQueries({
            queryKey: [QUERY_KEY.USER_ACCOUNT_LIST],
        });

        setSelectedAreaIds([]); // SYSTEM_WIDE không cần area_ids
        setShowAssignUser(true);
    };

    const handleResetAssignUser = () => {
        setSelectedAreaIds([]);
    };

    const deleteUserRolMutation = useGraphQLMutation<{ remove_user_role: boolean }, { id: string }>(
        REMOVE_USER_ROLE,
        '',
        {
            onSuccess: () => {
                setUserAreaRoleId('');
                showToast(true, ['User removed successfully']);
                setShowConfirmDelete(false);
                queryClient.invalidateQueries({
                    queryKey: [QUERY_KEY.ROLE, roleSelectedId],
                });
            },
        }
    );

    const handleDeleteUser = (id: string) => {
        setUserAreaRoleId(id);
        setShowConfirmDelete(true);
    };

    const deleteUser = () => {
        if (roleDetail) {
            deleteUserRolMutation.mutate({
                id: userAreaRoleId,
            });
        }
    };

    return (
        <div>
            <Helmet>
                <title>{'Role Assignment'}</title>
            </Helmet>
            <div className="content-body">
                <div className="col-12">
                    <div className="card">
                        <div className="card-header d-flex justify-content-between align-items-center">
                            <h4 className="card-title">Role Assignment</h4>
                        </div>
                        <div className="card-body">
                            <div className="nav-align-top nav-tabs-shadow">
                                <RoleTab
                                    activeTab={activeHeaderTab}
                                    roles={roleList
                                        .filter((role) => !role.is_gatekeeper_child)
                                        .sort((a, b) => a.type - b.type)}
                                    onChangeRole={setActiveheaderTab}
                                />
                                <div className="tab-content">
                                    <div
                                        className={`tab-pane fade ${
                                            activeHeaderTab === 'gatekeeper' ? 'active show' : ''
                                        }`}
                                        id="navs-gatekeeper"
                                        role="tabpanel"
                                    >
                                        {activeHeaderTab === 'gatekeeper' && (
                                            <RoleGatekeeper
                                                roles={getkeeperRoles}
                                                subAreas={subAreaList}
                                                onChangeRole={setActiveGatekeeperTab}
                                                activeGatekeeperTab={activeGatekeeperTab}
                                                role={roleDetail}
                                                onOpenAssignUser={handleOpenAssignUser}
                                                resetCheckboxes={resetCheckboxes}
                                            />
                                        )}
                                    </div>
                                    {roleList
                                        .filter((role) => !role.is_gatekeeper_child)
                                        .map((role) => (
                                            <div
                                                className={`tab-pane fade ${
                                                    activeHeaderTab === role.id ? 'active show' : ''
                                                }`}
                                                id={`navs-${role.id}`}
                                                role="tabpanel"
                                                key={role.id}
                                            >
                                                {activeHeaderTab === role.id && roleDetail && (
                                                    <>
                                                        {roleDetail.type === RoleType.AREA_BASED ? (
                                                            <RoleAreaBased
                                                                role={roleDetail}
                                                                subAreas={subAreaList}
                                                                onOpenAssignUser={handleOpenAssignUser}
                                                                resetCheckboxes={resetCheckboxes}
                                                            />
                                                        ) : roleDetail.type === RoleType.SYSTEM_WIDE ? (
                                                            <RoleSystemWide
                                                                role={roleDetail}
                                                                onOpenAssignUser={handleOpenAssignUserSystemWide}
                                                                onDeleteUser={handleDeleteUser}
                                                            />
                                                        ) : (
                                                            <div className="p-3">
                                                                <p>Unknown role type: {roleDetail.type}</p>
                                                            </div>
                                                        )}
                                                    </>
                                                )}
                                            </div>
                                        ))}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <ModalContent
                show={showAssignUser}
                changeShow={setShowAssignUser}
                title={roleDetail?.type === RoleType.SYSTEM_WIDE ? 'Add User' : 'Assign User Role'}
                size="xl"
                content={
                    <AssignUserForm
                        onUserSelect={handleUserSelect}
                        onClose={handleCloseAssignUser}
                        onReset={handleResetAssignUser}
                        role={roleDetail}
                        auth_group={AuthGroups.MOC_MANAGER}
                    />
                }
            />

            <ModalConfirm
                changeShow={(s: boolean) => setShowConfirmDelete(s)}
                show={showConfirmDelete}
                text="Are you sure you want to delete this user?"
                btnDisabled={false}
                submitAction={deleteUser}
            />
        </div>
    );
}
