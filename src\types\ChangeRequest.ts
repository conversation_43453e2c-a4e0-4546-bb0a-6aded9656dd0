import { BaseModel } from './common';
import { FormQueryItem } from './Form';
import { WorkflowInstance, WorkflowStatus } from './Workflow';

export interface ActiveWorkflow extends BaseModel {
    name: string;
    bpmnXml: string;
    status: WorkflowStatus;
    camunda_key: string;
    camunda_id: string;
    version: number;
    first_task_form_id: string;
    created_at: string;
    updated_at: string;
    workflow_steps: WorkflowStepsType[];
    workflow_instances: WorkflowInstance;
    forms: FormQueryItem;
}

export interface ActiveWorkflowQuery {
    active_workflow_detail: ActiveWorkflow;
}

export interface WorkflowStepsType {
    id: string;
    step_key: string;
    step_name: string;
}
