{"error": {"common": "An error occurred while processing your request", "required": "This field is required", "passwordLength": "Use a password with a minimum length of 12 characters", "passwordNotSame": "Passwords do not match", "passwordUpperCase": "Password must contain at least one uppercase letter", "passwordLowerCase": "Password must contain at least one lowercase letter", "passwordNumber": "Password must contain at least one number", "passwordSpecial": "Password must contain at least one special character", "email": "Invalid email", "number": "This field must be a number", "min_0": "The minimum value is 0", "greaterThan_0": "The value must be greater than 0", "min_1": "The minimum value is 1", "max_99": "The maximum value is 99", "chooseImage": "Invalid file, please select another image file"}, "success": {"update": "Update successfully", "delete": "Delete successfully"}, "constants": {"pending": "Lock", "active": "Active", "male": "Male", "female": "Female", "other": "Other", "yes": "Yes", "no": "No", "in-progress": "In Progress", "approved": "Approved", "terminated": "Terminated", "rejected": "Rejected", "draft": "Draft", "completed": "Completed", "submitted": "Submitted", "inactive": "Inactive", "deleted": "Deleted"}, "confirm": {"delete": "Do you really want to delete this object?", "status": "Do you really want change status to {{data}}?"}, "admin": {"single": "Administrator", "multiple": "Administrator", "add": "Add administrator", "edit": "Update administrator"}, "contributor": {"multiple": "Contributor", "single": "Contributor", "add": "Add contributor", "edit": "Update contributor"}, "workflow": {"list": "Workflow Version", "add": "Add workflow", "edit": "Update workflow", "name": "Name", "version": "Version", "status": "Status"}, "form": {"form": "Form", "add": "Add form", "edit": "Edit form", "name": "Name", "version": "Version", "status": "Status", "create": "Create New Form"}}