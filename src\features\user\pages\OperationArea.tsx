import { QUERY_KEY } from '../../../constants/common';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { AREAS_LIST_ALL } from '../../../services/AreaService';
import Area, { AllAreaQueryRes, AreaType } from '../../../types/OperationalArea';
import OperationalAreaTree from '../components/OperationalAreaTree';
import { Helmet } from 'react-helmet-async';
import { keepPreviousData } from '@tanstack/react-query';
import { useAuthStore } from '../../../stores/authStore';

export default function OperationArea() {
    const currentUser = useAuthStore((state) => state.user);

    const { data, refetch } = useGraphQLQuery<AllAreaQueryRes>(
        [QUERY_KEY.AREAS, AreaType.ZONE],
        AREAS_LIST_ALL,
        {
            filters: [`type:=(${AreaType.ZONE})`],
            sort: 'created_at:ASC',
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );
    const areaList: Area[] = data?.areas_list_all || [];
    return (
        <>
            <Helmet>
                <title>{'Operation Area'}</title>
            </Helmet>
            <div className="content-body">
                <div className="col-12">
                    <OperationalAreaTree areas={areaList} refetch={refetch} />
                </div>
            </div>
        </>
    );
}
