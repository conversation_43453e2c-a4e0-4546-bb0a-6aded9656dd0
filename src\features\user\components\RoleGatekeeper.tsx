import { useEffect, useState } from 'react';
import AreaTree from '../../../components/partials/AreaTree/AreaTree';
import Role from '../../../types/Role';
import Area from '../../../types/OperationalArea';

interface IProps {
    roles: Role[];
    subAreas: Area[];
    onChangeRole: (roleId: string) => void;
    activeGatekeeperTab: string;
    role?: Role;
    onOpenAssignUser: (areaIds: string[]) => void;
    resetCheckboxes?: boolean;
}

const RoleGatekeeper = ({
    roles,
    subAreas,
    onChangeRole,
    activeGatekeeperTab,
    role,
    onOpenAssignUser,
    resetCheckboxes,
}: Readonly<IProps>) => {
    const [checkedAreas, setCheckedAreas] = useState<string[]>([]);
    const [isCheckAll, setIsCheckAll] = useState(false);

    useEffect(() => {
        setCheckedAreas([]);
        setIsCheckAll(false);
    }, [role]);

    useEffect(() => {
        if (resetCheckboxes) {
            setCheckedAreas([]);
            setIsCheckAll(false);
        }
    }, [resetCheckboxes]);

    const handleCheckAll = (checked: boolean) => {
        setIsCheckAll(checked);
        if (checked) {
            setCheckedAreas(subAreas.map((area) => area.id));
        } else {
            setCheckedAreas([]);
        }
    };

    const handleCheckArea = (areaId: string, checked: boolean) => {
        if (checked) {
            const newCheckedAreas = [...checkedAreas, areaId];
            setCheckedAreas(newCheckedAreas);
            if (newCheckedAreas.length === subAreas.length) {
                setIsCheckAll(true);
            }
        } else {
            const newCheckedAreas = checkedAreas.filter((id) => id !== areaId);
            setCheckedAreas(newCheckedAreas);
            setIsCheckAll(false);
        }
    };

    const handleChangeUser = () => {
        if (checkedAreas.length > 0) {
            onOpenAssignUser(checkedAreas);
        }
    };

    const handleChangeUserInRow = (areaId: string) => {
        onOpenAssignUser([areaId]);
    };

    return (
        <div className="d-flex border rounded shadow bg-white min-vh-50">
            {/* Left: AreaTree */}
            <div className="border-end py-1" style={{ minWidth: 220, width: '25%' }}>
                <div className="fw-semibold mb-1 px-1">MOC Gatekeeper:</div>
                <div className="d-flex flex-column gap-1">
                    {roles.map((area) => (
                        <AreaTree
                            key={area.id}
                            id={area.id}
                            name={area.name}
                            handleClick={(id) => onChangeRole(id.toString())}
                            active={activeGatekeeperTab === area.id}
                        />
                    ))}
                </div>
            </div>
            {/* Right: Manage Area Responsibility */}
            <div className="flex-grow-1 p-1">
                <div className="d-flex justify-content-between align-items-center mb-1">
                    <div className="fw-semibold fs-5">Manage Area Responsibility</div>
                    <button
                        className="btn btn-primary btn-sm"
                        disabled={checkedAreas.length === 0}
                        onClick={handleChangeUser}
                    >
                        Change user
                    </button>
                </div>
                <div className="table-responsive">
                    <table className="table table-bordered table-sm align-middle mb-0">
                        <thead className="table-light">
                            <tr>
                                <th className="text-center" style={{ width: 40 }}>
                                    <input
                                        type="checkbox"
                                        className="form-check-input"
                                        checked={isCheckAll}
                                        onChange={(e) => handleCheckAll(e.target.checked)}
                                    />
                                </th>
                                <th>Sub Area</th>
                                <th>User Assigned</th>
                                <th className="text-center" style={{ width: 100 }}>
                                    Action
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {subAreas.map((sub, idx) => {
                                const userAreaRole = role?.userAreaRoles.find((uar) => uar.area_id === sub.id);
                                const isChecked = checkedAreas.includes(sub.id);
                                return (
                                    <tr key={sub.id}>
                                        <td className="text-center">
                                            <input
                                                type="checkbox"
                                                className="form-check-input"
                                                checked={isChecked}
                                                onChange={(e) => handleCheckArea(sub.id, e.target.checked)}
                                            />
                                        </td>
                                        <td>{sub.name}</td>
                                        <td>{userAreaRole?.user.full_name}</td>
                                        <td className="text-center">
                                            <button
                                                className="btn btn-sm btn-outline-primary"
                                                onClick={() => handleChangeUserInRow(sub.id)}
                                            >
                                                Change
                                            </button>
                                        </td>
                                    </tr>
                                );
                            })}
                        </tbody>
                    </table>
                </div>
                <div className="d-flex justify-content-end mt-4">
                    <button
                        className="btn btn-primary btn-sm"
                        disabled={checkedAreas.length === 0}
                        onClick={handleChangeUser}
                    >
                        Change user
                    </button>
                </div>
            </div>
        </div>
    );
};

export default RoleGatekeeper;
