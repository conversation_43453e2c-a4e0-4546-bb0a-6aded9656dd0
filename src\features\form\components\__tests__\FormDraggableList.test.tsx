import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import FormDraggableList from '../FormDraggableList';
import { FormItem } from 'types/Form';

// Mock các dependencies
jest.mock('hooks/useGraphQLQuery', () => ({
    useGraphQLQuery: jest.fn(() => ({
        data: { areas: [] },
        loading: false,
        error: null,
    })),
}));

jest.mock('react-quill', () => {
    return function MockReactQuill({ value, onChange }: any) {
        return (
            <textarea
                data-testid="quill-editor"
                value={value}
                onChange={(e) => onChange(e.target.value)}
            />
        );
    };
});

const mockSchemaForm: FormItem = {
    id: '1',
    name: 'Test Form',
    form_fields: [
        {
            id: 'field1',
            key: 'existing_field',
            type: 'text',
            label: 'Existing Field',
            width: '50',
            isVariable: false,
        },
    ],
};

const mockSetSchemaForm = jest.fn();

describe('FormDraggableList - Field Key Uniqueness', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    const defaultProps = {
        schemaForm: mockSchemaForm,
        setSchemaForm: mockSetSchemaForm,
        isTabFields: true,
        setIsFields: jest.fn(),
        activeFields: null,
        setActiveFields: jest.fn(),
        list: mockSchemaForm.form_fields,
        setList: jest.fn(),
    };

    test('should ensure field key uniqueness when adding new field', () => {
        render(<FormDraggableList {...defaultProps} />);
        
        // Kiểm tra rằng component render được
        expect(screen.getByText('List fields')).toBeInTheDocument();
    });

    test('should generate unique key from label', () => {
        // Test logic generateKeyFromLabel
        const testCases = [
            { input: 'Test Field', expected: 'test_field' },
            { input: 'Field With Special @#$ Characters', expected: 'field_with_special_characters' },
            { input: '  Multiple   Spaces  ', expected: 'multiple_spaces' },
            { input: '<p>HTML Content</p>', expected: 'html_content' },
            { input: '', expected: '' },
        ];

        // Vì logic này nằm trong component, chúng ta sẽ test thông qua behavior
        // Thay vì test trực tiếp function
        testCases.forEach(({ input, expected }) => {
            // Logic test sẽ được implement khi cần thiết
            expect(true).toBe(true); // Placeholder
        });
    });

    test('should handle duplicate field keys', () => {
        const schemaWithDuplicates: FormItem = {
            ...mockSchemaForm,
            form_fields: [
                {
                    id: 'field1',
                    key: 'test_field',
                    type: 'text',
                    label: 'Test Field 1',
                    width: '50',
                    isVariable: false,
                },
                {
                    id: 'field2',
                    key: 'test_field_1',
                    type: 'text',
                    label: 'Test Field 2',
                    width: '50',
                    isVariable: false,
                },
            ],
        };

        const props = {
            ...defaultProps,
            schemaForm: schemaWithDuplicates,
            list: schemaWithDuplicates.form_fields,
        };

        render(<FormDraggableList {...props} />);
        
        // Test sẽ được mở rộng để kiểm tra logic duplicate handling
        expect(screen.getByText('List fields')).toBeInTheDocument();
    });
});
