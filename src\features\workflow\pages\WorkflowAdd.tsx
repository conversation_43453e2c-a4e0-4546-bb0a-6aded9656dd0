import BpmnView from '../components/BpmnView';
import { Helmet } from 'react-helmet-async';
import ContentHeader from 'components/partials/ContentHeader';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

export const WorkflowAdd = () => {
    const { t } = useTranslation();
    return (
        <>
            <Helmet title="Create Workflow" />
            <ContentHeader
                title={
                    <>
                        <Link to="/workflow">Workflow Version</Link>
                    </>
                }
                breadcrumbs={[{ text: t('Create Workflow') }]}
            />
            <BpmnView id={''} />
        </>
    );
};
