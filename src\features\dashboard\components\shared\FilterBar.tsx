import React, { useState, useEffect, useCallback } from 'react';
import Select from 'react-select';
import { DateRangePicker as RsuiteDateRangePicker } from 'rsuite';
import 'rsuite/DateRangePicker/styles/index.css';
import { useSearchParams } from 'react-router-dom';
import { debounce } from 'lodash';
import { FORMAT_DATE, formatDateTime } from '../../../../utils/date';
import { TrackingStatusNames, workflowInstanceTypeNames } from '../../../../types/Workflow';
import { isDateValid } from '../../../../utils/common';
import { DateRange } from 'rsuite/esm/DateRangePicker';

export interface FilterField {
    key: string;
    type: 'select' | 'date_range';
    placeholder: string;
    options?: { label: string; value: string }[];
    staticOptions?: 'status' | 'type' | 'workflow_version'; // For predefined options
    isMulti?: boolean; // Add this property to support multi-select
}

export interface FilterBarProps {
    fields: FilterField[];
    isLoading?: boolean;
    debounceMs?: number;
}

const FilterBar: React.FC<FilterBarProps> = ({ fields, isLoading = false, debounceMs = 500 }) => {
    const [searchParams, setSearchParams] = useSearchParams();
    const [filters, setFilters] = useState<Record<string, string>>({});
    const [dateRangeValues, setDateRangeValues] = useState<Record<string, [Date, Date] | null>>({});

    // Initialize filters from URL params
    useEffect(() => {
        const initialFilters: Record<string, string> = {};
        fields.forEach((field) => {
            initialFilters[field.key] = searchParams.get(field.key) || '';
        });
        setFilters(initialFilters);
    }, [searchParams, fields]);

    // Initialize date range values
    useEffect(() => {
        const newDateRangeValues: Record<string, [Date, Date] | null> = {};
        fields.forEach((field) => {
            if (field.type === 'date_range' && filters[field.key]) {
                const dates = filters[field.key].split('-');
                if (dates.length === 2) {
                    const startDate = new Date(dates[0]);
                    const endDate = new Date(dates[1]);
                    if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
                        newDateRangeValues[field.key] = [startDate, endDate];
                    }
                }
            } else if (field.type === 'date_range') {
                newDateRangeValues[field.key] = null;
            }
        });
        setDateRangeValues(newDateRangeValues);
    }, [filters, fields]);

    // Debounced function to update URL params and notify parent
    const debouncedUpdateParams = useCallback(
        debounce((newFilters: Record<string, string>) => {
            const updatedParams = new URLSearchParams(searchParams);

            Object.entries(newFilters).forEach(([key, value]) => {
                if (value) {
                    updatedParams.set(key, value);
                } else {
                    updatedParams.delete(key);
                }
            });

            setSearchParams(updatedParams);
        }, debounceMs),
        [searchParams, setSearchParams, debounceMs]
    );

    const handleFilterChange = (field: string, value: string) => {
        const newFilters = { ...filters, [field]: value };
        setFilters(newFilters);
        debouncedUpdateParams(newFilters);
    };

    const handleSelectChange = (field: string) => (selectedOption: any) => {
        if (Array.isArray(selectedOption)) {
            // Handle multi-select
            const values = selectedOption.map((option) => option.value);
            handleFilterChange(field, values.join(','));
        } else {
            // Handle single select
            const value = selectedOption ? selectedOption.value : '';
            handleFilterChange(field, value);
        }
    };

    const handleDateRangeChange = (field: string) => (value: DateRange) => {
        if (value) {
            const startDate =
                Array.isArray(value) && value[0] && isDateValid(value[0])
                    ? formatDateTime(value[0], FORMAT_DATE.DB_DATE_2)
                    : '';
            const endDate =
                Array.isArray(value) && value[1] && isDateValid(value[1])
                    ? formatDateTime(value[1], FORMAT_DATE.DB_DATE_2)
                    : '';
            if (startDate && endDate) {
                handleFilterChange(field, `${startDate}-${endDate}`);
            }
            if (value.length === 2) {
                setDateRangeValues((prev) => ({ ...prev, [field]: value }));
            }
        }
    };

    const handleDateRangeClean = (field: string) => () => {
        setDateRangeValues((prev) => ({ ...prev, [field]: null }));
        handleFilterChange(field, '');
    };

    const getSelectOptions = (field: FilterField) => {
        if (field.staticOptions === 'status') {
            return TrackingStatusNames.map((item) => ({
                label: item.name,
                value: item.id,
            }));
        }
        if (field.staticOptions === 'type') {
            return workflowInstanceTypeNames.map((item) => ({
                label: item.name,
                value: item.id,
            }));
        }
        return field.options || [];
    };

    const getSelectValue = (field: FilterField) => {
        if (!filters[field.key]) return null;

        const options = getSelectOptions(field);

        if (field.isMulti) {
            // For multi-select, return array of selected options
            const values = filters[field.key].split(',');
            return values.map((value) => options.find((option) => option.value === value)).filter(Boolean);
        } else {
            // For single select, return the selected option
            return options.find((option) => option.value === filters[field.key]) || null;
        }
    };

    const renderField = (field: FilterField) => {
        if (field.type === 'select') {
            return (
                <Select
                    key={field.key}
                    placeholder={field.placeholder}
                    value={getSelectValue(field)}
                    onChange={handleSelectChange(field.key)}
                    options={getSelectOptions(field)}
                    isMulti={field.isMulti}
                    isClearable
                    isLoading={isLoading}
                    className="react-select-container"
                    classNamePrefix="react-select"
                />
            );
        }

        if (field.type === 'date_range') {
            return (
                <RsuiteDateRangePicker
                    key={field.key}
                    format="dd/MM/yyyy"
                    character=" – "
                    className="form-control new-date-range-picker"
                    onOk={handleDateRangeChange(field.key)}
                    onChange={(value) => value && handleDateRangeChange(field.key)(value)}
                    onClean={handleDateRangeClean(field.key)}
                    value={dateRangeValues[field.key]}
                    placeholder={field.placeholder}
                />
            );
        }

        return null;
    };

    return (
        <div className="row">
            {fields.map((field) => (
                <div key={field.key} className="col-md-3 col-12 mobile-460:tw-mb-3 mobile-460:tw-mt-3">
                    {renderField(field)}
                </div>
            ))}
        </div>
    );
};

export default FilterBar;
