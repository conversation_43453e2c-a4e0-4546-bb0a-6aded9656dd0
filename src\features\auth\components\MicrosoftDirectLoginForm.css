/* Modern Login Form Styles */
.modern-login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: #ffffff;
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.login-card {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 100%;
    max-width: 450px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #00AFF0, #0066CC);
}

/* Logo Section */
.logo-section {
    margin-bottom: 30px;
}

.company-logo {
    max-width: 200px;
    height: auto;
    margin: 0 auto;
    display: block;
}

/* Title Section */
.title-section {
    margin-bottom: 40px;
    text-align: center;
}

.login-title {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    letter-spacing: -0.3px;
    line-height: 1.2;
}

.login-subtitle {
    font-size: 18px;
    font-weight: 400;
    color: #6c757d;
    margin-bottom: 0;
    letter-spacing: -0.1px;
}

/* Form Section */
.login-form {
    margin-bottom: 30px;
}

/* Floating Label Input */
.floating-input-group {
    position: relative;
    margin-bottom: 30px;
}

.floating-input {
    width: 100%;
    padding: 8px 16px;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    font-size: 16px;
    color: #2c3e50;
    background-color: #ffffff;
    transition: all 0.3s ease;
    outline: none;
    box-sizing: border-box;
}

.floating-input:focus {
    border-color: #00AFF0;
    box-shadow: 0 0 0 3px rgba(0, 175, 240, 0.1);
    outline: none;
}

.floating-input:disabled {
    background-color: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
}

.floating-label {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    color: #95a5a6;
    pointer-events: none;
    transition: all 0.3s ease;
    background-color: #ffffff;
    padding: 0 4px;
}

.floating-input:focus + .floating-label,
.floating-input:not(:placeholder-shown) + .floating-label {
    top: 0;
    transform: translateY(-50%);
    font-size: 12px;
    color: #00AFF0;
    font-weight: 500;
}

.floating-input:focus + .floating-label {
    color: #00AFF0;
}

/* Button Group - Centered */
.button-group-center {
    display: flex;
    justify-content: center;
    margin-top: 25px;
}

.btn-primary-center {
    padding: 8px 16px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    outline: none;
    text-transform: none;
    letter-spacing: 0.3px;
    background: linear-gradient(135deg, #00AFF0, #0066CC);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(0, 175, 240, 0.3);
    width: 100%;
    box-sizing: border-box;
}

.btn-primary-center:hover:not(:disabled) {
    background: linear-gradient(135deg, #0099D6, #0052A3);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 175, 240, 0.4);
}

.btn-primary-center:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}



/* Company Info */
.company-info {
    font-size: 12px;
    color: #7f8c8d;
    font-weight: 500;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    border-top: 1px solid #ecf0f1;
    padding-top: 20px;
    margin-top: 20px;
}

/* Responsive Design */
@media (max-width: 480px) {
    .modern-login-container {
        padding: 15px;
    }
    
    .login-card {
        padding: 30px 25px;
        margin: 10px;
    }
    
    .company-logo {
        max-width: 150px;
    }
    
    .login-title {
        font-size: 24px;
    }
    
    .btn-primary-center {
        width: 100%;
    }
}

@media (max-width: 360px) {
    .login-card {
        padding: 25px 20px;
    }
    
    .modern-input {
        padding: 14px 16px;
        font-size: 15px;
    }
    
    .btn-primary-center {
        padding: 8px 16px;
        font-size: 15px;
    }
}

/* Loading Animation */
.btn-primary-center:disabled {
    position: relative;
    overflow: hidden;
}

.btn-primary-center:disabled::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Force light mode - override dark mode */
@media (prefers-color-scheme: dark) {
    .modern-login-container {
        background: #ffffff !important;
    }

    .login-card {
        background: #ffffff !important;
        color: #2c3e50 !important;
    }

    .login-title {
        color: #2c3e50 !important;
    }

    .login-subtitle {
        color: #6c757d !important;
    }

    .floating-input {
        background-color: #ffffff !important;
        border: 1px solid #e1e8ed !important;
        color: #2c3e50 !important;
    }

    .floating-label {
        background-color: #ffffff !important;
        color: #95a5a6 !important;
    }

    .floating-input:focus + .floating-label,
    .floating-input:not(:placeholder-shown) + .floating-label {
        color: #00AFF0 !important;
    }

    .company-info {
        color: #7f8c8d !important;
        border-color: #ecf0f1 !important;
    }
}

/* Smooth transitions for all interactive elements */
/* * {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
} */

/* Focus styles for accessibility */
.btn-primary-center:focus {
    outline: 2px solid #00AFF0;
    outline-offset: 2px;
}

.floating-input:focus {
    outline: none !important;
}


