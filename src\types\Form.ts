import { BaseModelString } from './common';
import { WorkflowFormType } from './Workflow';

export type Form = FormItem[];

export interface FormItem {
    form_key: string;
    form_fields: FormFields[];
    settings: FormSettings;
}

export interface FormFields {
    id: string;
    key: string;
    type: InputType;
    label?: string;
    width?: string;
    height?: string;
    placeholder?: string;
    option?: FormFieldsCheckboxOption[];
    value?: string;
    buttonType?: ButtonType;
    variableKey?: string;
    variableValue?: string;
    valueAppear?: string;
    keyAppear?: string;
    keyRequired?: string;
    valueRequired?: string[];
    dataSource?: 'custom' | 'pssr_list';
    isVariable?: boolean;
    isRequired?: boolean;
    isTable?: boolean;
    isFirstAreaOwner?: string;
    isRemark?: boolean;
    labelColor?: string;
    backgroundColor?: string;
    tooltip?: string;
    defaultData?: string;
}

export type DataSourceType = 'custom' | 'pssr_list';

export interface FormFieldsCheckboxOption {
    id: string;
    value: string;
    label: string;
}
export interface FormSettings {
    title: string;
    formAlignment: 'left' | 'center' | 'right';
    previousForm?: string[];
    formTitleColor?: string;
    fieldLabelColor?: string;
    formBackgroundColor?: string;
}

export interface FormSettingBtn {
    type: ButtonType;
    title: string;
    variableKey: string;
    variableValue: string;
}

export type InputType =
    | 'text'
    | 'number'
    | 'checkbox'
    | 'radio'
    | 'file'
    | 'date'
    | 'date_range'
    | 'time'
    | 'free_text'
    | 'text_area'
    | 'select'
    | 'area_of_implementation'
    | 'select_gate_keepers'
    | 'aims_system'
    | 'e_smart_iso_system'
    | 'pha_database'
    | 'button'
    | 'pssr_report'
    | 'close_out_checklist'
    | 'validate_checklist'
    | 'tooltip'
    | 'approve_for_implementation';
export interface FormFieldsDefaultType {
    id: string;
    type: InputType;
    title: string;
    quantity?: number;
}

export interface FormQuery {
    form_list: {
        page: number;
        limit: number;
        filters: string[];
        search: string;
        data: FormQueryItem[];
        totalCount: number;
    };
}

export interface FormDetail {
    form_detail: FormQueryItem;
}
export interface FormQueryItem extends BaseModelString {
    name: string;
    schema: FormItem;
    description: string;
    status: FormStatus;
    workflow_forms: WorkflowFormType[];
    version: number;
}
export enum FormStatus {
    DRAFT = 'DRAFT',
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
}

export enum FormType {
    TEMPLATE = 'TEMPLATE',
    DYNAMIC = 'DYNAMIC',
}

export enum ButtonType {
    CANCEL = 'cancel',
    TERMINATE = 'terminate',
    REJECT = 'reject',
    APPROVE = 'approve',
    SAVE_DRAFT = 'save_draft',
    SUBMIT = 'submit',
}

export type OptionType = { value: FormType; label: string };
export type OptionButtonType = { value: ButtonType; label: string };
export type OptionFormAlignmentType = { value: 'left' | 'center' | 'right'; label: string };

export type OptionFormTypeAppear = { value: 'radio' | 'checkbox' | 'select' | ''; label: string };

export type OptionFormDataSource = { value: 'custom' | 'pssr_list'; label: string };

export interface FormDashboardQuery {
    form_dashboard: {
        activated: number;
        inactivated: number;
        draft: number;
        total: number;
    };
}
