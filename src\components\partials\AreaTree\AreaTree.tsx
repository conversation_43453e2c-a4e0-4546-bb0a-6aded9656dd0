import classNames from 'classnames';
import { Folder } from 'react-feather';

interface IProps {
    id: number | string;
    name: string;
    handleClick: (id: number | string) => void;
    active: boolean;
}

export default function AreaTree({ id, name, handleClick, active }: Readonly<IProps>) {
    return (
        <div
            className={classNames('d-flex items-center gap-2 p-2 cursor-pointer rounded', {
                'bg-gray-100 text-black': active,
            })}
            onClick={() => handleClick(id)}
            style={{
                backgroundColor: active ? '#00AFF0' : 'transparent',
                color: active ? '#fff' : '#000',
            }}
        >
            <Folder size={18} className={classNames('text-gray-600', { 'text-white': active })} />
            <span className={classNames('font-medium', { 'text-white': active })}>{name}</span>
        </div>
    );
}
