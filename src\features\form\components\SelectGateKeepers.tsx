/* eslint-disable react-hooks/exhaustive-deps */
import { keepPreviousData } from '@tanstack/react-query';
import { QUERY_KEY } from 'constants/common';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { useMemo } from 'react';
import { Control, useFormContext, Controller } from 'react-hook-form';
import { AREAS_LIST, AREAS_LIST_BY_SUB_AREA } from 'services/AreaService';
import { AreaQueryRes, AreasListBySubArea, AreaType } from 'types/OperationalArea';
import { UserZoneType } from 'types/Workflow';

interface SelectGateKeepersProps {
    name: string;
    control: Control<any, any>;
    disabled?: boolean;
    dataPlanAndAreaUnit?: any;
    userZones?: UserZoneType[];
}

interface User {
    id: string;
    name: string;
}

interface Role {
    roleKey: string;
    roleLabel: string;
    userOptions: User[];
}

interface Zone {
    zoneId: string;
    zoneName: string;
    roles: Role[];
}

export default function SelectGateKeepers({ name, disabled, dataPlanAndAreaUnit, userZones }: SelectGateKeepersProps) {
    const { data: dataArea } = useGraphQLQuery<AreaQueryRes>(
        [QUERY_KEY.AREAS, AreaType.ZONE],
        AREAS_LIST,
        {
            filters: [`type:=(${AreaType.ZONE})`],
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    function transformToPayload(
        data: Record<string, string[]> = {},
        areaList: any[] = []
    ): { zone_id: string; sub_area_ids: string[] }[] {
        const getAncestorIds = (id: string, nodes: any[], path: string[] = []): string[] => {
            for (const node of nodes) {
                if (node.id === id) return [...path];
                if (node.children) {
                    const result = getAncestorIds(id, node.children, [...path, node.id]);
                    if (result.length) return result;
                }
            }
            return [];
        };

        const result: { zone_id: string; sub_area_ids: string[] }[] = [];
        for (const [zoneId, ids] of Object.entries(data || {})) {
            const sub_area_ids = ids.filter((id) => getAncestorIds(id, areaList).length === 2);
            if (sub_area_ids.length > 0) {
                result.push({ zone_id: zoneId, sub_area_ids });
            }
        }

        return result;
    }

    const payload = useMemo(
        () => transformToPayload(dataPlanAndAreaUnit || {}, dataArea?.areas_list || []),
        [dataPlanAndAreaUnit, dataArea?.areas_list]
    );

    const { data: dataAreasListBySubArea } = useGraphQLQuery<AreasListBySubArea>(
        [QUERY_KEY.AREAS_LIST_BY_SUB_AREA, payload, dataPlanAndAreaUnit, dataArea?.areas_list],
        AREAS_LIST_BY_SUB_AREA,
        { body: { zone_groups: payload } },
        '',
        {
            enabled:
                !!dataArea &&
                Array.isArray(dataArea.areas_list) &&
                dataArea.areas_list.length > 0 &&
                Array.isArray(payload) &&
                payload.length > 0 &&
                payload.some((p) => Array.isArray(p.sub_area_ids) && p.sub_area_ids.length > 0),
            placeholderData: keepPreviousData,
        }
    );

    function dedupeUsersById(users: { id: string; full_name: string }[]) {
        const map = new Map<string, { id: string; full_name: string }>();
        users.forEach((user) => {
            map.set(user.id, user);
        });
        return Array.from(map.values());
    }

    const zones: Zone[] = (dataAreasListBySubArea?.areas_list_by_sub_area || []).map((zone) => ({
        zoneId: zone.id,
        zoneName: zone.name,
        roles: (zone.roles || []).map((role) => {
            const uniqueUsers = dedupeUsersById(role.users);
            return {
                roleKey: role.id,
                roleLabel: role.name,
                userOptions: uniqueUsers.map((u) => ({
                    id: u.id,
                    name: u.full_name,
                })),
            };
        }),
    }));

    return (
        <>
            {zones.length === 0 && (
                <div className="mb-1">
                    <table className="tw-w-full tw-border border-collapse tw-text-left">
                        <thead>
                            <tr className="tw-bg-gray-100">
                                <th className="tw-border tw-p-2">Gate Keeper (Zone Name)</th>
                                <th className="tw-border tw-p-2 tw-w-[50%]">Your Selection</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td className="tw-border tw-p-2">GateKeeper Name</td>
                                <td className="tw-border tw-p-2">
                                    <select
                                        className="form-select tw-w-full tw-pr-[36px] tw-pointer-events-none tw-bg-transparent"
                                        disabled={disabled}
                                    >
                                        <option value="">N/A</option>
                                    </select>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            )}
            {zones?.map((zone) => (
                <GateKeeperTableField
                    key={zone.zoneId}
                    name={`${name}.${zone.zoneId}`}
                    zoneName={zone.zoneName}
                    roles={zone.roles}
                    disabled={disabled}
                    zoneId={zone.zoneId}
                    userZones={userZones}
                />
            ))}
        </>
    );
}

function GateKeeperTableField({
    name,
    roles,
    zoneName,
    disabled,
    zoneId,
    userZones,
}: {
    name: string;
    roles: Role[];
    zoneName: string;
    disabled?: boolean;
    zoneId: string;
    userZones?: UserZoneType[];
}) {
    const { control } = useFormContext();

    const hasZone = userZones?.some((zone) => zone.id === zoneId) ?? true;

    return (
        <Controller
            name={name}
            control={control}
            defaultValue={{}}
            render={({ field: { value = {}, onChange } }) => {
                const handleUserChange = (role: Role, userId: string) => {
                    const updated = {
                        ...value,
                        [role.roleKey]: userId,
                    };

                    if (!userId) {
                        delete updated[role.roleKey];
                    }

                    onChange(updated);
                };

                return (
                    <div className="mb-1">
                        <table className="tw-w-full tw-border border-collapse tw-text-left">
                            <thead>
                                <tr className="tw-bg-gray-100">
                                    <th className="tw-border tw-p-2">Gate Keeper ({zoneName})</th>
                                    <th className="tw-border tw-p-2 tw-w-[50%]">Your Selection</th>
                                </tr>
                            </thead>
                            <tbody>
                                {roles.map((role) => {
                                    const selectedUserId = value[role.roleKey] || '';

                                    return (
                                        <tr key={role.roleKey}>
                                            <td className="tw-border tw-p-2">{role.roleLabel}</td>
                                            <td className="tw-border tw-p-2">
                                                <select
                                                    className="form-select tw-w-full tw-pr-[36px] tw-bg-transparent"
                                                    value={selectedUserId}
                                                    onChange={(e) => handleUserChange(role, e.target.value)}
                                                    disabled={disabled || !hasZone}
                                                >
                                                    <option value="">N/A</option>
                                                    {role.userOptions.map((u) => (
                                                        <option key={u.id} value={u.id}>
                                                            {u.name}
                                                        </option>
                                                    ))}
                                                </select>
                                            </td>
                                        </tr>
                                    );
                                })}
                            </tbody>
                        </table>
                    </div>
                );
            }}
        />
    );
}
