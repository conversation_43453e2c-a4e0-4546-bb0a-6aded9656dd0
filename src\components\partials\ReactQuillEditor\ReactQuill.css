/* ReactQuill Editor Container */
.react-quill-editor-container {
    border-radius: 0.357rem;
    overflow: hidden;
}

/* Main Quill container styling */
.react-quill-editor-container .ql-container {
    border-color: #d8d6de;
    border-radius: 0 0 0.357rem 0.357rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

/* Toolbar styling */
.react-quill-editor-container .ql-toolbar {
    border-color: #d8d6de;
    border-radius: 0.357rem 0.357rem 0 0;
    background-color: #f8f8f8;
    border-bottom: 1px solid #d8d6de;
}

/* Editor content area */
.react-quill-editor-container .ql-editor {
    color: #4e5154;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    line-height: 1.42;
    padding: 12px 15px;
}

/* Placeholder styling */
.react-quill-editor-container .ql-editor.ql-blank::before {
    color: #999;
    font-style: italic;
}

/* Button hover effects */
.react-quill-editor-container .ql-toolbar button:hover,
.react-quill-editor-container .ql-toolbar button:focus {
    background: #e8e8e8;
    border-radius: 3px;
}

/* Active button styling */
.react-quill-editor-container .ql-toolbar button.ql-active {
    background: #d8d8d8;
    border-radius: 3px;
}

/* Dropdown styling */
.react-quill-editor-container .ql-toolbar .ql-picker {
    color: #4e5154;
}

.react-quill-editor-container .ql-toolbar .ql-picker-options {
    background: white;
    border: 1px solid #d8d6de;
    border-radius: 0.357rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Color picker styling */
.react-quill-editor-container .ql-toolbar .ql-color .ql-picker-options,
.react-quill-editor-container .ql-toolbar .ql-background .ql-picker-options {
    padding: 8px;
    width: 200px;
}

/* Color swatch styling */
.react-quill-editor-container .ql-color-picker .ql-picker-item {
    border: 1px solid transparent;
    border-radius: 2px;
    margin: 1px;
}

.react-quill-editor-container .ql-color-picker .ql-picker-item:hover {
    border-color: #00AFF0 !important;
}

.react-quill-editor-container .ql-color-picker .ql-picker-item.ql-selected {
    border-color: #00AFF0 !important;
    box-shadow: 0 0 0 1px #00AFF0 !important;
}

/* Size dropdown styling */
.react-quill-editor-container .ql-toolbar .ql-size .ql-picker-options {
    padding: 4px 0;
}

.react-quill-editor-container .ql-toolbar .ql-size .ql-picker-item {
    padding: 4px 12px;
}

.react-quill-editor-container .ql-toolbar .ql-size .ql-picker-item:hover {
    background-color: #f0f0f0;
}

/* Focus styling */
.react-quill-editor-container .ql-container.ql-snow {
    border-color: #d8d6de;
}

.react-quill-editor-container:focus-within .ql-container.ql-snow {
    border-color: #00AFF0;
    box-shadow: 0 0 0 1px #00AFF0;
}

.react-quill-editor-container:focus-within .ql-toolbar.ql-snow {
    border-color: #00AFF0;
}

/* List styling */
.react-quill-editor-container .ql-editor ol,
.react-quill-editor-container .ql-editor ul {
    padding-left: 1.5em;
}

/* Link styling */
.react-quill-editor-container .ql-editor a {
    color: #00AFF0;
    text-decoration: underline;
}

/* Custom font sizes */
.react-quill-editor-container .ql-size-8px { font-size: 8px; }
.react-quill-editor-container .ql-size-10px { font-size: 10px; }
.react-quill-editor-container .ql-size-12px { font-size: 12px; }
.react-quill-editor-container .ql-size-14px { font-size: 14px; }
.react-quill-editor-container .ql-size-16px { font-size: 16px; }
.react-quill-editor-container .ql-size-18px { font-size: 18px; }
.react-quill-editor-container .ql-size-20px { font-size: 20px; }
.react-quill-editor-container .ql-size-24px { font-size: 24px; }
.react-quill-editor-container .ql-size-32px { font-size: 32px; }
.react-quill-editor-container .ql-size-48px { font-size: 48px; }

/* Font size dropdown labels */
.react-quill-editor-container .ql-picker.ql-size .ql-picker-label::before,
.react-quill-editor-container .ql-picker.ql-size .ql-picker-item::before {
    content: "Normal";
}

.react-quill-editor-container .ql-picker.ql-size .ql-picker-label[data-value="8px"]::before,
.react-quill-editor-container .ql-picker.ql-size .ql-picker-item[data-value="8px"]::before {
    content: "8px";
}

.react-quill-editor-container .ql-picker.ql-size .ql-picker-label[data-value="10px"]::before,
.react-quill-editor-container .ql-picker.ql-size .ql-picker-item[data-value="10px"]::before {
    content: "10px";
}

.react-quill-editor-container .ql-picker.ql-size .ql-picker-label[data-value="12px"]::before,
.react-quill-editor-container .ql-picker.ql-size .ql-picker-item[data-value="12px"]::before {
    content: "12px";
}

.react-quill-editor-container .ql-picker.ql-size .ql-picker-label[data-value="14px"]::before,
.react-quill-editor-container .ql-picker.ql-size .ql-picker-item[data-value="14px"]::before {
    content: "14px";
}

.react-quill-editor-container .ql-picker.ql-size .ql-picker-label[data-value="16px"]::before,
.react-quill-editor-container .ql-picker.ql-size .ql-picker-item[data-value="16px"]::before {
    content: "16px";
}

.react-quill-editor-container .ql-picker.ql-size .ql-picker-label[data-value="18px"]::before,
.react-quill-editor-container .ql-picker.ql-size .ql-picker-item[data-value="18px"]::before {
    content: "18px";
}

.react-quill-editor-container .ql-picker.ql-size .ql-picker-label[data-value="20px"]::before,
.react-quill-editor-container .ql-picker.ql-size .ql-picker-item[data-value="20px"]::before {
    content: "20px";
}

.react-quill-editor-container .ql-picker.ql-size .ql-picker-label[data-value="24px"]::before,
.react-quill-editor-container .ql-picker.ql-size .ql-picker-item[data-value="24px"]::before {
    content: "24px";
}

.react-quill-editor-container .ql-picker.ql-size .ql-picker-label[data-value="32px"]::before,
.react-quill-editor-container .ql-picker.ql-size .ql-picker-item[data-value="32px"]::before {
    content: "32px";
}

.react-quill-editor-container .ql-picker.ql-size .ql-picker-label[data-value="48px"]::before,
.react-quill-editor-container .ql-picker.ql-size .ql-picker-item[data-value="48px"]::before {
    content: "48px";
}