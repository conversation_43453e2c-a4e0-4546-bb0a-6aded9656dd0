import { FC, useState } from 'react';
import { PiPencilSimpleLine } from 'react-icons/pi';
import EditFormInfoModal from './EditFormInfoModal';

interface CreateFormHeaderProps {
    formName: string;
    onFormNameChange: (name: string) => void;
    formDescription: string;
    onFormDescriptionChange: (description: string) => void;
    onSaveDraft: () => void;
    onPreview: () => void;
}

const CreateFormHeader: FC<CreateFormHeaderProps> = ({
    formName,
    formDescription,
    onFormNameChange,
    onFormDescriptionChange,
    onPreview,
    onSaveDraft,
}) => {
    const [isOpenEditModal, setIsOpenEditModal] = useState(false);

    return (
        <>
            <EditFormInfoModal
                isOpen={isOpenEditModal}
                onClose={() => setIsOpenEditModal(false)}
                onSave={(name, description) => {
                    onFormNameChange(name);
                    onFormDescriptionChange(description);
                }}
                initFormName={formName}
                initFormDescription={formDescription}
            />
            <div className="tw-flex tw-flex-col tw-gap-y-2 tw-mb-8 -tw-mt-4">
                <div className="tw-flex tw-items-center tw-justify-between tw-gap-x-4">
                    <div className="tw-flex tw-items-center tw-gap-x-1">
                        <span className="tw-text-[#212636] tw-text-3xl tw-font-medium">{formName || 'Form Name'}</span>
                        <button onClick={() => setIsOpenEditModal(true)} className="tw-text-[#667085] tw-text-[20px]">
                            <PiPencilSimpleLine />
                        </button>
                    </div>

                    <div className="tw-flex tw-gap-x-4">
                        <button
                            onClick={onSaveDraft}
                            className="tw-py-2 tw-px-6 tw-border tw-border-[#212636] tw-rounded-xl tw-text-[#212636] tw-font-medium"
                        >
                            Save Draft
                        </button>
                        <button
                            onClick={onPreview}
                            className="tw-py-2 tw-px-6 tw-border tw-rounded-xl tw-font-medium tw-bg-[#00AFF0] tw-text-white"
                        >
                            Preview
                        </button>
                    </div>
                </div>
                <span className="tw-text-[#667085] tw-text-[14px]">{formDescription || 'Form Description'}</span>
            </div>
        </>
    );
};

export default CreateFormHeader;
