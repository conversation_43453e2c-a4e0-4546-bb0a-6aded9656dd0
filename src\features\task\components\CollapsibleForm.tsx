import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown } from 'react-feather';

interface IProps {
    title: string;
    children: React.ReactNode;
    defaultOpen?: boolean;
}

export default function CollapsibleForm({ title, children, defaultOpen = false }: Readonly<IProps>) {
    const [isOpen, setIsOpen] = useState(defaultOpen);
    const contentRef = useRef<HTMLDivElement>(null);

    const toggleOpen = () => {
        setIsOpen(!isOpen);
    };

    useEffect(() => {
        const contentEl = contentRef.current;
        if (!contentEl) return;

        if (isOpen) {
            // Đặt height thành auto tạm thời để lấy chiều cao thực tế
            contentEl.style.height = 'auto';
            const contentHeight = contentEl.scrollHeight;

            // Đặt lại height về 0 và sau đó animate đến chiều cao thực tế
            contentEl.style.height = '0px';

            // Force reflow
            void contentEl.offsetHeight;

            // Đặt height thành chiều cao thực tế để animate
            contentEl.style.height = `${contentHeight}px`;

            // Thiết lập timeout để chuyển sang auto sau khi animation hoàn tất
            const transitionEndHandler = () => {
                if (isOpen) {
                    contentEl.style.height = 'auto';
                }
                contentEl.removeEventListener('transitionend', transitionEndHandler);
            };

            contentEl.addEventListener('transitionend', transitionEndHandler);
        } else {
            // Lấy chiều cao hiện tại trước khi collapse
            const contentHeight = contentEl.scrollHeight;

            // Đặt height cụ thể để có thể animate
            contentEl.style.height = `${contentHeight}px`;

            // Force reflow
            void contentEl.offsetHeight;

            // Animate về 0
            contentEl.style.height = '0px';
        }
    }, [isOpen]);

    return (
        <div className="card mb-2 border">
            <div
                className="card-header d-flex justify-content-between align-items-center cursor-pointer py-1"
                onClick={toggleOpen}
                style={{ backgroundColor: '#f8f8f8' }}
            >
                <h4 className="card-title mb-0">{title}</h4>
                <div
                    style={{
                        transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                        transition: 'transform 0.3s ease-in-out',
                    }}
                >
                    <ChevronDown size={18} />
                </div>
            </div>
            <div
                ref={contentRef}
                className="collapsible-content overflow-hidden"
                style={{
                    transition: 'height 0.3s ease-in-out, opacity 0.3s ease-in-out',
                    opacity: isOpen ? '1' : '0.8',
                }}
            >
                <div className="card-body p-0">{children}</div>
            </div>
        </div>
    );
}
