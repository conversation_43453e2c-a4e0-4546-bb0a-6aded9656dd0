import ContentHeader from 'components/partials/ContentHeader';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import FormBuilder from './FormBuilder';
import { useAuthStore } from 'stores/authStore';
import { useMemo } from 'react';
import { AuthGroups } from 'types/User';
import { Link } from 'react-router-dom';

export default function AddForm() {
    const { t } = useTranslation();

    const currentUser = useAuthStore((state) => state.user);
    const isSuperAdmin = useMemo(() => currentUser?.auth_group === AuthGroups.SUPER_ADMIN, [currentUser?.auth_group]);

    return (
        <>
            <Helmet>
                <title>{t('form.create')}</title>
            </Helmet>
            <ContentHeader title={<Link to="/changeRequest">Form</Link>} breadcrumbs={[{ text: t('form.create') }]} />
            <FormBuilder isSuperAdmin={isSuperAdmin} />
        </>
    );
}
