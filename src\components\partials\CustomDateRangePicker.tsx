import React, { forwardRef, useEffect, useState } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
// import './CustomDateRangePicker.css'; // Your custom CSS
import DateInputShowComponent from './DateInputShowComponent';

interface CustomDateInputProps extends React.HTMLProps<HTMLDivElement> {
    value?: string;
}

interface CustomDateRangePickerProps {
    value?: string; // Format: "mm/dd/yyyy-mm/dd/yyyy"
    onChange?: (value: string) => void;
    dateFormat?: string;
    placeholderText?: string;
    isClearable?: boolean;
}

const CustomDateRangePicker: React.FC<CustomDateRangePickerProps> = ({
    value,
    onChange,
    dateFormat = 'MM/dd/yyyy',
    placeholderText = 'Select Date Range',
    isClearable = true,
}) => {
    const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);
    const [startDate, setStartDate] = useState<Date | undefined>(undefined);
    const [endDate, setEndDate] = useState<Date | undefined>(undefined);

    // Xử lý giá trị ban đầu từ props
    useEffect(() => {
        if (value) {
            const dates = value.split('-');
            if (dates.length === 2) {
                const start = new Date(dates[0]);
                const end = new Date(dates[1]);

                if (!isNaN(start.getTime()) && !isNaN(end.getTime())) {
                    setDateRange([start, end]);
                }
            }
        } else {
            setDateRange([null, null]);
        }
    }, [value]);

    const CustomDateInput = forwardRef<HTMLDivElement, CustomDateInputProps>(({ value, onClick }, ref) => (
        <DateInputShowComponent id="date-picker" placeholder={value || placeholderText} onClick={onClick} ref={ref} />
    ));

    CustomDateInput.displayName = 'CustomDateInput';

    // Cập nhật startDate và endDate khi dateRange thay đổi
    useEffect(() => {
        if (dateRange[0]) {
            setStartDate(dateRange[0]);
        } else {
            setStartDate(undefined);
        }

        if (dateRange[1]) {
            setEndDate(dateRange[1]);
        } else {
            setEndDate(undefined);
        }
    }, [dateRange]);

    // Định dạng ngày thành chuỗi mm/dd/yyyy
    const formatDate = (date: Date | null): string => {
        if (!date) return '';
        const month = date.getMonth() + 1;
        const day = date.getDate();
        const year = date.getFullYear();
        return `${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}/${year}`;
    };

    // Xử lý khi ngày thay đổi
    const handleDateChange = (update: [Date | null, Date | null]) => {
        setDateRange(update);

        // Gọi callback nếu cả hai ngày đều được chọn
        if (update[0] && update[1] && onChange) {
            const formattedValue = `${formatDate(update[0])}-${formatDate(update[1])}`;
            onChange(formattedValue);
        } else if (!update[0] && !update[1] && onChange) {
            // Nếu không có ngày nào được chọn, trả về chuỗi rỗng
            onChange('');
        }
    };

    return (
        <DatePicker
            selectsRange={true}
            startDate={startDate}
            endDate={endDate}
            onChange={handleDateChange}
            dateFormat={dateFormat}
            calendarStartDay={1}
            customInput={<CustomDateInput />}
            wrapperClassName="col-12"
            className="form-control"
            isClearable={isClearable}
        />
    );
};

export default CustomDateRangePicker;
