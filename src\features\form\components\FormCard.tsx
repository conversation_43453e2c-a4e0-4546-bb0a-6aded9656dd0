import { FC } from 'react';
import { useTranslation } from 'react-i18next';

interface FormCardProps {
    quantity: number;
    title: string;
    color: string;
}

const FormCard: FC<FormCardProps> = ({ color, quantity, title }) => {
    const { t } = useTranslation();

    return (
        <div className="tw-p-6 tw-rounded-[20px] tw-bg-white tw-border tw-border-gray-200 tw-flex tw-flex-col tw-gap-y-2">
            <div className="tw-flex tw-items-center tw-justify-between tw-gap-x-2">
                <h2 className="tw-text-2xl tw-text-[#212636] tw-font-semibold">{quantity}</h2>
                <div
                    style={{
                        backgroundColor: color,
                    }}
                    className="tw-size-10 tw-rounded-full tw-bg-[#B0E6FA]"
                ></div>
            </div>
            <p className="tw-text-sm tw-text-[#667085]">{t(title)}</p>
        </div>
    );
};

export default FormCard;
