import { getStatusBadgeWorkflow } from '../../../../utils/common';
import PaginationTable from '../../../../components/partials/PaginationTable';
import { DashboardMocWorkflowInstances } from '../../../../types/Dashboard';
import { workflowInstanceTypeNames } from '../../../../types/Workflow';
import { FORMAT_DATE, formatDateTime } from '../../../../utils/date';
import { DataList } from '../../../../types/common';

interface Iprops {
    mocWorkflowInstanceData: DataList<DashboardMocWorkflowInstances> | undefined;
    onChangePage: (page: number) => void;
}

export function TableData({ mocWorkflowInstanceData, onChangePage }: Readonly<Iprops>) {
    const mocWorkflowInstance = mocWorkflowInstanceData?.data || [];

    return (
        <>
            <div className="table-responsive">
                <table className="table table-sm align-middle mb-0">
                    <thead>
                        <tr>
                            <th>MOC No.</th>
                            <th>Title</th>
                            <th>Area</th>
                            <th>Type</th>
                            <th>Current Step</th>
                            <th className="text-center">Status</th>
                            <th>Originator</th>
                            <th>Due Date</th>
                            <th>Overdue</th>
                            <th>Backlog</th>
                        </tr>
                    </thead>
                    <tbody>
                        {mocWorkflowInstance.map((item) => (
                            <tr key={item.business_key}>
                                <td>{item.business_key}</td>
                                <td>{item.name}</td>
                                <td>{item.area?.name}</td>
                                <td>
                                    {
                                        workflowInstanceTypeNames.find((instanceType) => instanceType.id === item.type)
                                            ?.name
                                    }
                                </td>
                                <td>{item.current_user_task?.workflow_step?.step_name}</td>
                                <td className="text-center">{getStatusBadgeWorkflow(item.status)}</td>
                                <td>{item.creator?.full_name}</td>
                                <td>{item.due_date ? formatDateTime(item.due_date, FORMAT_DATE.SHORT_DATE) : '-'}</td>
                                <td>
                                    {!item.due_date
                                        ? '-'
                                        : item.overdue
                                        ? `${item.overdue} Day${item.overdue > 1 ? 's' : ''}`
                                        : '-'}
                                </td>
                                <td>
                                    {!item.due_date
                                        ? '-'
                                        : item.backlog
                                        ? +`Yes (${item.backlog} Day${item.backlog > 1 ? 's' : ''})`
                                        : 'No'}
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <PaginationTable
                countItem={mocWorkflowInstanceData?.totalCount || 0}
                totalPage={mocWorkflowInstanceData?.totalPages || 1}
                currentPage={mocWorkflowInstanceData?.currentPage || 1}
                handlePageChange={(event, page) => onChangePage(page)}
            />
        </>
    );
}
