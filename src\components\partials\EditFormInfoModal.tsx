/* eslint-disable react-hooks/exhaustive-deps */
import { yupResolver } from '@hookform/resolvers/yup';
import { Modal } from '@mui/material';
import { FC, useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { IoCloseOutline } from 'react-icons/io5';
import cn from 'utils/cn';
import * as yup from 'yup';

interface EditFormInfoModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSave: (name: string, description: string) => void;
    initFormName?: string;
    initFormDescription?: string;
}

interface FormValues {
    formName: string;
    description: string;
}

const EditFormInfoModal: FC<EditFormInfoModalProps> = ({
    isOpen,
    onClose,
    onSave,
    initFormDescription,
    initFormName,
}) => {
    const { t } = useTranslation();
    const schema = useMemo(
        () =>
            yup.object({
                formName: yup.string().required(t('error.required')).trim(),
                description: yup.string().required(t('error.required')).trim(),
            }),
        [t]
    );

    const {
        register,
        reset,
        formState: { errors },
        handleSubmit,
    } = useForm<FormValues>({
        resolver: yupResolver(schema),
    });

    useEffect(() => {
        if (isOpen) {
            reset({
                formName: initFormName || '',
                description: initFormDescription || '',
            });
        }
    }, [initFormName, initFormDescription, isOpen, reset]);

    const onSubmit = (data: FormValues) => {
        onSave(data.formName, data.description);
        onClose();
    };

    return (
        <Modal open={isOpen} onClose={onClose} className="tw-grid tw-place-items-center">
            <div className="tw-w-[600px] tw-pt-[16px] tw-pb-[20px] tw-px-[24px] tw-rounded-2xl tw-bg-white tw-flex tw-flex-col tw-gap-y-8 tw-outline-none">
                <div className="tw-flex tw-items-center tw-justify-between tw-gap-x-4">
                    <span className="tw-text-[#212636] tw-text-xl tw-font-medium">Save New Form</span>
                    <button onClick={onClose} className="tw-text-[#667085] tw-text-3xl">
                        <IoCloseOutline />
                    </button>
                </div>

                <div className="tw-flex tw-flex-col tw-gap-y-6 tw-text-[#212636]">
                    <div className="tw-flex tw-flex-col tw-gap-y-1">
                        <label htmlFor="formName" className="tw-font-medium tw-text-sm">
                            Form Name <span className="tw-text-red-600">*</span>
                        </label>
                        <input
                            type="text"
                            id="formName"
                            className={cn(
                                'tw-rounded-xl tw-border tw-border-[#DCDFE4] tw-outline-none tw-px-[12px] tw-py-[8px]',
                                {
                                    'tw-border-red-600': errors.formName?.message,
                                }
                            )}
                            {...register('formName')}
                        />
                        {errors?.formName?.message && (
                            <span className="tw-text-red-600 tw-text-sm">{errors?.formName?.message}</span>
                        )}
                    </div>

                    <div className="tw-flex tw-flex-col tw-gap-y-1">
                        <label htmlFor="formDesc" className="tw-font-medium tw-text-sm">
                            Form Description <span className="tw-text-red-600">*</span>
                        </label>
                        <textarea
                            id="formDesc"
                            className={cn(
                                'tw-rounded-xl tw-border tw-border-[#DCDFE4] tw-outline-none tw-px-[12px] tw-py-[8px] tw-min-h-[140px]',
                                {
                                    'tw-border-red-600': errors.description?.message,
                                }
                            )}
                            {...register('description')}
                        />
                        {errors?.description?.message && (
                            <span className="tw-text-red-600 tw-text-sm">{errors?.description?.message}</span>
                        )}
                    </div>
                </div>

                <div className="tw-flex tw-items-center tw-justify-end tw-gap-x-3">
                    <button
                        onClick={onClose}
                        className="tw-py-2 tw-px-6 tw-border tw-border-[#212636] tw-rounded-xl tw-text-[#212636] tw-font-medium"
                    >
                        Cancel
                    </button>
                    <button
                        onClick={handleSubmit(onSubmit)}
                        className="tw-py-2 tw-px-6 tw-border tw-rounded-xl tw-font-medium tw-bg-[#00AFF0] tw-text-white"
                    >
                        Save
                    </button>
                </div>
            </div>
        </Modal>
    );
};

export default EditFormInfoModal;
