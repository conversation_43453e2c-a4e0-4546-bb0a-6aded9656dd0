import SearchForm from 'components/partials/SearchForm';
import { useTranslation } from 'react-i18next';
import { TaskStatusNames } from '../../../types/Task';
import { convertConstantToSelectOptions } from 'utils/common';

interface IProps {
    isLoading: boolean;
}

export default function SearchChangeRequestForm({ isLoading }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <SearchForm
            fields={[
                { name: 'search', type: 'text', label: 'Keyword', wrapClassName: 'col-md-4 col-12' },
                {
                    name: 'status_id',
                    type: 'select',
                    label: 'Status',
                    wrapClassName: 'col-md-4 col-12',
                    options: {
                        multiple: true,
                        choices: convertConstantToSelectOptions(TaskStatusNames, t, true),
                    },
                },
            ]}
            isLoading={isLoading}
        />
    );
}
