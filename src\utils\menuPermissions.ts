import { AuthGroups } from 'types/User';
import Action from 'types/Action';

// <PERSON><PERSON><PERSON> nghĩa quyền truy cập cho từng route
export const ROUTE_PERMISSIONS: Record<string, AuthGroups[]> = {
    // Dashboard routes - ADMIN, SUPER_ADMIN, MOC_MANAGER
    '/moc-overview': [AuthGroups.ADMIN, AuthGroups.SUPER_ADMIN, AuthGroups.MOC_MANAGER],
    '/moc-process': [AuthGroups.ADMIN, AuthGroups.SUPER_ADMIN, AuthGroups.MOC_MANAGER],
    '/moc-health': [AuthGroups.ADMIN, AuthGroups.SUPER_ADMIN, AuthGroups.MOC_MANAGER],

    // Workflow routes - ADMIN, SUPER_ADMIN
    '/workflow': [AuthGroups.ADMIN, AuthGroups.SUPER_ADMIN],
    '/workflow/edit/:id': [AuthGroups.ADMIN, AuthGroups.SUPER_ADMIN],

    // Workflow Instance routes - ADMI<PERSON>, SUPER_ADMIN, MOC_MANAGER
    '/workflowInstance': [AuthGroups.ADMIN, AuthGroups.SUPER_ADMIN, AuthGroups.MOC_MANAGER],
    '/workflowInstance/:id': [AuthGroups.ADMIN, AuthGroups.SUPER_ADMIN, AuthGroups.MOC_MANAGER],

    // Form routes
    '/form': [AuthGroups.ADMIN, AuthGroups.SUPER_ADMIN], // ADMIN, SUPER_ADMIN
    '/form/edit/:id': [AuthGroups.ADMIN, AuthGroups.SUPER_ADMIN], // ADMIN, SUPER_ADMIN
    '/form/add': [AuthGroups.SUPER_ADMIN], // Chỉ SUPER_ADMIN

    // User routes - ADMIN, SUPER_ADMIN
    '/user/list': [AuthGroups.ADMIN, AuthGroups.SUPER_ADMIN],
    '/user/edit/:id': [AuthGroups.ADMIN, AuthGroups.SUPER_ADMIN],
    '/user/operation-area': [AuthGroups.ADMIN, AuthGroups.SUPER_ADMIN],
    '/user/role-assignments': [AuthGroups.ADMIN, AuthGroups.SUPER_ADMIN],

    // Routes mọi auth_group đều có thể truy cập
    '/changeRequest': [AuthGroups.MOC_USER, AuthGroups.MOC_MANAGER, AuthGroups.ADMIN, AuthGroups.SUPER_ADMIN],
    '/task': [AuthGroups.MOC_USER, AuthGroups.MOC_MANAGER, AuthGroups.ADMIN, AuthGroups.SUPER_ADMIN],
    '/user/profile': [AuthGroups.MOC_USER, AuthGroups.MOC_MANAGER, AuthGroups.ADMIN, AuthGroups.SUPER_ADMIN], // Profile của user

    // Route log chỉ có SUPER_ADMIN có thể truy cập
    '/audit-log': [AuthGroups.SUPER_ADMIN],
    '/error-log': [AuthGroups.SUPER_ADMIN],
};

/**
 * Kiểm tra xem user có quyền truy cập route không
 */
export const hasRoutePermission = (route: string, userAuthGroup: AuthGroups): boolean => {
    const allowedGroups = ROUTE_PERMISSIONS[route];
    if (!allowedGroups) {
        // Nếu route không được định nghĩa trong permissions, mặc định cho phép truy cập
        return true;
    }
    return allowedGroups.includes(userAuthGroup);
};

/**
 * Filter menu actions dựa vào auth_group của user
 */
export const filterMenuByAuthGroup = (actions: Action[], userAuthGroup: AuthGroups | undefined): Action[] => {
    if (!userAuthGroup) {
        return [];
    }

    return actions
        .map((action) => {
            // Filter children trước
            const filteredChildren = action.children
                ? action.children.filter((child) => hasRoutePermission(child.url, userAuthGroup))
                : [];

            // Nếu action có children, chỉ giữ lại nếu có ít nhất 1 child được phép truy cập
            if (action.children && action.children.length > 0) {
                if (filteredChildren.length > 0) {
                    return {
                        ...action,
                        children: filteredChildren,
                    };
                }
                return null; // Loại bỏ action nếu không có child nào được phép
            }

            // Nếu action không có children hoặc children rỗng, kiểm tra quyền truy cập trực tiếp
            if (action.url) {
                // Nếu có URL, kiểm tra quyền truy cập
                if (hasRoutePermission(action.url, userAuthGroup)) {
                    return action;
                }
            } else {
                // Nếu không có URL (parent menu), luôn cho phép hiển thị
                // vì logic filter children đã xử lý ở trên
                return action;
            }

            return null; // Loại bỏ action nếu không có quyền
        })
        .filter((action): action is Action => action !== null);
};

/**
 * Kiểm tra xem user có quyền truy cập vào ít nhất một menu item không
 */
export const hasAnyMenuAccess = (actions: Action[], userAuthGroup: AuthGroups | undefined): boolean => {
    if (!userAuthGroup) {
        return false;
    }

    const filteredActions = filterMenuByAuthGroup(actions, userAuthGroup);
    return filteredActions.length > 0;
};
