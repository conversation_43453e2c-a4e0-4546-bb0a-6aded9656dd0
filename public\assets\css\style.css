html .content.app-content {
    min-height: calc(100vh - 50px);
}
.dropdown-menu.show {
    position: absolute;
    inset: 0px auto auto 0px;
    margin: 0px;
}
.main-menu .shadow-bottom.onScrollMenu {
    display: block;
}
.main-menu .navbar-header {
    height: 90px;
}
.main-menu.expanded .navbar-header .navbar-brand .brand-logo img {
    max-width: 170px;
    max-height: 80px;
    margin-left: 30px;
    transition: all 0.5s ease-in-out;
}
.MuiTabs-scroller.MuiTabs-fixed {
    max-width: 100%;
    overflow: auto !important;
}

.MuiTabs-scroller.MuiTabs-fixed::-webkit-scrollbar {
    width: 4px;
    height: 8px;
}

.MuiTabs-scroller.MuiTabs-fixed::-webkit-scrollbar:hover {
    width: 4px;
    height: 8px;
}

.MuiTabs-scroller.MuiTabs-fixed::-webkit-scrollbar-thumb {
    border-radius: 10px;
    width: 4px;
    height: 8px;
    background-color: #ccc;
}

.bg-warning .card-body {
    padding: 0.5rem 0.5rem;
}

.cursor-pointer {
    cursor: pointer;
}

span.error {
    color: #ea5455;
}

.input-group-text-readOnly {
    background-color: #efefef;
}

.no-border-lr {
    border-left: none !important;
    border-right: none !important;
}

.badge-balance {
    margin: 0 0.5rem;
}
.checkbox-btn {
    margin-right: 5px;
}
.btn-send-group {
    display: flex;
    flex-direction: column;
}
button.color-primary:hover {
    background-color: rgba(0, 0, 0, 0.04);
}
.thAction1 {
    width: 50px;
}
.thAction2 {
    width: 100px;
}
.thAction3 {
    width: 130px;
}
.thWidth150 {
    width: 150px;
}
.products-dropdown {
    width: calc(100% - 30px);
    position: absolute;
    z-index: 999;
}
.products-dropdown .card-footer {
    padding-bottom: 0;
}
.text-heading {
    --bs-text-opacity: 1;
    color: #444050 !important;
}
.w-px-150 {
    width: 150px !important;
}
.image-product {
    max-height: 50px;
    width: 50px;
}
.border-none {
    border-style: none;
}
.bootstrap-touchspin.input-group {
    margin: 0 auto;
}
.icon-remove-scan {
    position: absolute;
    right: -15px;
    top: -15px;
}
.form-check-input:disabled {
    background-color: #efefef;
    border-color: #efefef;
}
.order-image-position {
    height: 200px;
}
.order-image-position-sleeve {
    height: 100px;
}
.form-check-input:disabled:checked {
    background-color: #00aff0 !important;
    border-color: #00aff0 !important;
}
.h-50px {
    height: 50px;
}
.lh-30px {
    line-height: 30px;
}
.object-fit-contain {
    object-fit: contain;
}
@media only screen and (max-width: 1366px) {
    .order-image-position {
        height: 100px;
    }
}
.role-multiline-ellipsis {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    max-width: 250px;
}
.custom-date-range-picker .react-daterange-picker__wrapper {
    border: 0;
}
.custom-date-range-picker .react-daterange-picker__calendar-button,
.custom-date-range-picker .react-daterange-picker__clear-button {
    padding-bottom: 0;
    padding-top: 0;
}

.custom-date-range-picker .react-daterange-picker__calendar-button svg,
.custom-date-range-picker .react-daterange-picker__clear-button svg {
    stroke: #5e5873!important;
    opacity: .5;
}

.custom-date-range-picker .react-daterange-picker__calendar-button:hover svg,
.custom-date-range-picker .react-daterange-picker__clear-button:hover svg {
    stroke: #00AFF0!important;
}

.custom-date-range-picker input:focus {
    outline: none;
}
.custom-date-range-picker .react-daterange-picker__inputGroup__input:invalid {
    background: none;
}
.custom-date-range-picker .react-daterange-picker__inputGroup:nth-of-type(1) {
    max-width: 120px;
}
.custom-date-range-picker .react-daterange-picker__inputGroup:nth-of-type(2) {
    padding-left: 12px;
}

/* Apply height setting from CSS variable for DateRangePicker */
.custom-date-range-wrapper .new-date-range-picker {
    height: var(--date-range-height, 40px);
}

.custom-date-range-wrapper .new-date-range-picker .rs-input-group.rs-input-group-inside {
    height: 100%;
    display: flex;
    align-items: center;
}

.custom-date-range-wrapper .react-daterange-picker__wrapper {
    height: 100%;
    display: flex;
    align-items: center;
}

.custom-date-range-wrapper .react-daterange-picker__inputGroup {
    height: 100%;
    display: flex;
    align-items: center;
}

.custom-date-range-wrapper .react-daterange-picker__inputGroup__input {
    height: 100%;
    border: none;
    background: transparent;
    font-size: inherit;
}
.menu-collapsed .main-menu .navigation.navigation-main li ul li a {
    padding: 8px 12px;
    display: block !important;
    transition: all 0.5s ease;
}
.menu-collapsed .main-menu .navigation.navigation-main li ul li a span {
    display: none;
}
.dropdown-inline.dropdown-toggle::after {
    display: inline;
}

.new-date-range-picker .rs-input-group {
    border: 0;
}
.new-date-range-picker .rs-input-group.rs-input-group-inside .rs-input {
    color: #6e6b7b;
    padding: 0;
}
.new-date-range-picker .rs-input-group.rs-input-group-inside .rs-input:focus {
    border: 0;
    outline: none;
}
.new-date-range-picker .rs-input-group.rs-input-group-inside .rs-input-group-addon {
    padding: 0;
}
.new-date-range-picker .rs-input-group:not(.rs-input-group-disabled).rs-input-group-focus, .rs-input-group:focus-within {
    outline: none;
}
.vertical-layout.vertical-menu-modern.menu-expanded .main-menu {
    width: 270px;
}