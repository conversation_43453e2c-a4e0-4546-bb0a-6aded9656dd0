declare module 'bpmn-js/lib/NavigatedViewer' {
    interface ViewerOptions {
        container: string | HTMLElement;
        additionalModules?: any[];
        keyboard?: {
            bindTo: HTMLElement;
        };
    }

    type ImportXMLCallback = (err: Error | null, warnings: any[]) => void;

    export default class NavigatedViewer {
        constructor(options: ViewerOptions);
        importXML(xml: string, callback: ImportXMLCallback): void;
        get<T = any>(name: string): T;
        destroy(): void;
    }
}
