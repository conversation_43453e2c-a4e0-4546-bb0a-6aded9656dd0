import { useEffect } from 'react';

export function useClickOutside(ref: React.RefObject<HTMLElement>, onClickOutside: () => void, classNames: string[]) {
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            const target = event.target as HTMLElement;

            if (classNames.some((className) => target.closest(`.${className}`))) {
                return;
            }

            if (ref.current && !ref.current.contains(target)) {
                onClickOutside();
            }
        }

        document.addEventListener('mousedown', handleClickOutside);

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [ref, onClickOutside, classNames]);
}
