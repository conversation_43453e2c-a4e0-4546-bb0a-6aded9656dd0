import { Controller, Control } from 'react-hook-form';

interface ValidationChecklistProps {
    control: Control<any>;
    name: string;
    disabled: boolean;
}

const VALIDATION_ITEMS = [
    'Pre-Startup Safety Review (PSSR) items have been completed (Punch B)',
    'All relevant staff have been communicated and/or training',
    'Regulatory requirements and safety protocols have been updated and documented',
    'Environmental impacts have been updated and documented',
    'Process Hazard Analysis (PHA) have been reviewed',
];

const statusOptions = ['[Status]', 'Completed', 'On going', 'N/A'];

export default function ValidationChecklist({ control, name, disabled }: ValidationChecklistProps) {
    return (
        <div className="tw-overflow-auto">
            <table className="tw-w-full tw-border tw-border-collapse">
                <thead className="tw-bg-gray-100">
                    <tr>
                        <th className="tw-border tw-p-2">List</th>
                        <th className="tw-border tw-p-2">Validation Item</th>
                        <th className="tw-border tw-p-2 tw-text-center">Status</th>
                        <th className="tw-border tw-p-2">Remark</th>
                    </tr>
                </thead>
                <tbody>
                    {VALIDATION_ITEMS.map((item, index) => (
                        <tr key={index}>
                            <td className="tw-border tw-p-1 tw-text-center">{index + 1}</td>
                            <td className="tw-border tw-p-1">{item}</td>
                            <td className="tw-border tw-p-1">
                                <Controller
                                    control={control}
                                    defaultValue="[Status]"
                                    name={`${name}.${index}.status`}
                                    render={({ field }) => (
                                        <select className="tw-outline-none tw-pr-[20px]" {...field} disabled={disabled}>
                                            {statusOptions.map((status) => (
                                                <option key={status} value={status}>
                                                    {status}
                                                </option>
                                            ))}
                                        </select>
                                    )}
                                />
                            </td>

                            <td className="tw-border tw-p-1">
                                <Controller
                                    name={`${name}.${index}.remark`}
                                    control={control}
                                    render={({ field }) => (
                                        <textarea
                                            className="tw-w-full tw-outline-none tw-min-h-[40px]"
                                            {...field}
                                            disabled={disabled}
                                        />
                                    )}
                                />
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
