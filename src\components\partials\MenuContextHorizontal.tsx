import { Download, PlusCircle, Send, RefreshCw } from 'react-feather';
import { Link } from 'react-router-dom';
import { ItemLink } from 'types/common/Item';

interface IProps {
    contextMenu: ItemLink[];
}

export default function MenuContextHorizontal({ contextMenu }: Readonly<IProps>) {
    const handleClickMenu = (menu: ItemLink) => {
        if (menu.fnCallBack) {
            menu.fnCallBack.actionMenu(menu.id ?? 0);
        }
    };

    const getIcon = (iconType: string) => {
        switch (iconType) {
            case 'PLUS':
                return <PlusCircle size={14} className="me-1" />;
            case 'DOWNLOAD':
                return <Download size={14} className="me-1" />;
            case 'SEND':
                return <Send size={14} className="me-1" />;
            case 'REFRESH':
                return <RefreshCw size={14} className="me-1" />;
            default:
                return null;
        }
    };

    return (
        <div className="content-header-right text-end col-md-6 col-12 d-block">
            <div className="mb-1 breadcrumb-right">
                <div className="d-flex gap-2 justify-content-end">
                    {contextMenu.map((menu: ItemLink, index: number) => {
                        const iconJSX = menu.icon ? getIcon(menu.icon) : null;

                        return menu.to ? (
                            <Link
                                key={index}
                                to={menu.to}
                                className="btn btn-primary btn-sm waves-effect waves-float waves-light d-flex align-items-center"
                            >
                                {iconJSX}
                                <span className="align-middle">{menu.text}</span>
                            </Link>
                        ) : (
                            <button
                                key={index}
                                onClick={() => handleClickMenu(menu)}
                                className={`btn btn-primary btn-sm waves-effect waves-float waves-light d-flex align-items-center ${menu.btnClassName}`}
                                type="button"
                            >
                                {iconJSX}
                                <span className="align-middle">{menu.text}</span>
                            </button>
                        );
                    })}
                </div>
            </div>
        </div>
    );
}
