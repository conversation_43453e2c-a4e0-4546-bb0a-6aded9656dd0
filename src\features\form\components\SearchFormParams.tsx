import SearchForm from 'components/partials/SearchForm';
import { useTranslation } from 'react-i18next';
import { FormStatus } from 'types/Form';
import { convertConstantToSelectOptions } from 'utils/common';

interface IProps {
    isLoading: boolean;
}

const FormStatusNames = [
    { id: FormStatus.DRAFT, name: 'draft' },
    { id: FormStatus.ACTIVE, name: 'active' },
    { id: FormStatus.INACTIVE, name: 'inactive' },
];

export default function SearchFormParams({ isLoading }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <SearchForm
            fields={[
                {
                    name: 'search',
                    type: 'text',
                    label: ' Search Form',
                    wrapClassName: 'col-md-4 col-12',
                    placeholder: 'Enter form name or description',
                },
                {
                    name: 'status',
                    type: 'select',
                    label: 'Status',
                    wrapClassName: 'col-md-4 col-12',
                    options: {
                        multiple: true,
                        choices: convertConstantToSelectOptions(FormStatusNames, t, true),
                    },
                },
            ]}
            isLoading={isLoading}
        />
    );
}
