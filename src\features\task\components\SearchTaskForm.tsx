import SearchForm from 'components/partials/SearchForm';
import { useTranslation } from 'react-i18next';
import { TaskStatusNames } from '../../../types/Task';
import { convertConstantToSelectOptions } from 'utils/common';
import { TrackingStatusNames } from '../../../types/Workflow';

interface IProps {
    isLoading: boolean;
}

export default function SearchTaskForm({ isLoading }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <SearchForm
            fields={[
                {
                    name: 'search',
                    type: 'text',
                    label: 'Keyword',
                    wrapClassName: 'col-md-3 col-12',
                    placeholder: 'Enter task name, request title or task no',
                },
                {
                    name: 'created_at__range',
                    type: 'date-range',
                    label: 'Creation Date Range',
                    wrapClassName: 'col-md-3 col-12',
                    options: {
                        placeholderText: 'Select date range',
                        dateFormat: 'dd/MM/yyyy',
                        isClearable: true,
                    },
                },
                {
                    name: 'status',
                    type: 'select',
                    label: 'Status',
                    wrapClassName: 'col-md-3 col-12',
                    options: {
                        multiple: true,
                        choices: convertConstantToSelectOptions(TaskStatusNames, t, true),
                    },
                },
                {
                    name: 'tracking_status',
                    type: 'select',
                    label: 'Tracking Status',
                    wrapClassName: 'col-md-3 col-12',
                    options: {
                        multiple: true,
                        choices: TrackingStatusNames.map((item) => ({
                            label: item.name,
                            value: item.id,
                        })),
                    },
                },
            ]}
            isLoading={isLoading}
        />
    );
}
