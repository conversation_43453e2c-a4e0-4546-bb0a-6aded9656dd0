import { TextField } from '@mui/material';
import React, { forwardRef } from 'react';

type DateInputShowComponentProps = {
    id: string;
    placeholder: string;
    onClick: React.MouseEventHandler<HTMLElement> | undefined;
};

const DateInputShowComponent = forwardRef<HTMLDivElement, DateInputShowComponentProps>(
    ({ id, placeholder, onClick }, ref) => {
        const onClickHandler = (event: React.MouseEvent<HTMLElement>) => {
            onClick && onClick(event);
        };
        return (
            <TextField
                id={id}
                value={placeholder}
                onClick={(e) => onClickHandler(e)}
                variant="outlined"
                ref={ref}
                sx={{
                    mt: 0,
                    width: '100%',
                    '& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#00AFF0',
                        borderWidth: '1px',
                        boxShadow: 'none',
                    },
                    '& .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#00AFF0',
                    },
                    '& .MuiInputBase-input': {
                        color: '#6e6b7b',
                    },
                    '& .MuiOutlinedInput-root.Mui-focused .MuiInputBase-input': {
                        color: '#6e6b7b',
                    },
                }}
                size="small"
            />
        );
    }
);

DateInputShowComponent.displayName = 'DateInputShowComponent';

export default DateInputShowComponent;
