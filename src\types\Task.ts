import { FILTER_CONDITIONS } from '../constants/common';
import { BaseModelString, BaseSearch, DataList, FilterConfig } from './common';
import { IFile, ItemParam } from './common/Item';
import TaskComment from './TaskComment';
import {
    FormEntity,
    UserRolesType,
    UserTaskEntity,
    UserZoneType,
    WorkflowInstance,
    WorkflowStepsType,
} from './Workflow';

export enum TaskStatus {
    IN_PROGRESS = 'IN_PROGRESS',
    APPROVED = 'APPROVED',
    TERMINATED = 'TERMINATED',
    REJECTED = 'REJECTED',
    SUBMITTED = 'SUBMITTED',
    DELETED = 'DELETED',
}

export interface FileAttachment {
    workflow_step_id: string;
    workflow_step_name: string;
    workflow_step_order: number;
    files: (TaskFile | IFile)[];
}

export interface Task extends BaseModelString {
    workflow_instance_id: string;
    task_id: string;
    task_key: string;
    task_name: string;
    assignee: string;
    status: TaskStatus;
    started_at: string;
    completed_at: string;
    completed_by: string;
    comments: string;
    form_key: string;
    parsedFormData: JSON;
    form_data: JSON;
    variables: JSON;
    workflow_step_id: string;
    workflow_instance: WorkflowInstance;
    form: FormEntity;
    workflow_step: WorkflowStepsType;
    task_comments: TaskComment[];
    files: TaskFile[];
    task_no: number;
    order: number;
    element_variable: string;
    element_value: string;
    subprocess_element_variable: string;
    subprocess_element_value: string;
    user_tasks: UserTaskEntity[];
    tracking_status: string;
    userRoles: UserRolesType[];
    user_zones: UserZoneType[];
}

export interface TaskQuery {
    user_tasks_by_assignee: DataList<Task>;
}

export interface SearchTask extends BaseSearch {
    status?: string;
    assignee?: string;
    tracking_status?: string;
    created_at__from?: string;
    created_at__to?: string;
    created_at__range?: string;
}

export type SearchTaskParam = {
    [key in keyof SearchTask]: string;
};

export const TaskStatusNames: ItemParam[] = [
    { id: TaskStatus.IN_PROGRESS, name: 'in-progress' },
    { id: TaskStatus.APPROVED, name: 'approved' },
    { id: TaskStatus.TERMINATED, name: 'terminated' },
    { id: TaskStatus.REJECTED, name: 'rejected' },
    { id: TaskStatus.SUBMITTED, name: 'submitted' },
    { id: TaskStatus.DELETED, name: 'deleted' },
];

export const taskFilterConfig: FilterConfig = {
    status: { key: 'status', operator: FILTER_CONDITIONS.IN },
    assignee: { key: 'assignee', operator: FILTER_CONDITIONS.IN },
    tracking_status: { key: 'tracking_status', operator: FILTER_CONDITIONS.IN },
    created_at__from: { key: 'created_at', operator: FILTER_CONDITIONS.GREATER_OR_EQUAL },
    created_at__to: { key: 'created_at', operator: FILTER_CONDITIONS.LESS_OR_EQUAL },
};

export interface TaskDetailQuery {
    user_task_detail: Task;
}

export enum TaskType {
    Temporary = 'Temporary',
    Permanent = 'Permanent',
}

export const TaskTypeNames: ItemParam[] = [
    { id: TaskType.Temporary, name: 'Temporary' },
    { id: TaskType.Permanent, name: 'Permanent' },
];

export interface TaskFile extends IFile {
    user_task_id: string;
    user_task: UserTaskEntity;
}

export interface TaskDashboard {
    in_progress: number;
    completed: number;
    cancelled: number;
    overdue: number;
    backlog: number;
}
