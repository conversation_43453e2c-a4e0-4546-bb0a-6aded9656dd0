import { Eye } from 'react-feather';
import { errorCate<PERSON>yNames, <PERSON>rrorLog, SystemNames } from '../../../../types/Logs';
import { FORMAT_DATE, formatDateTime } from '../../../../utils/date';

interface IProps {
    errorLogs: ErrorLog[];
    onViewDetail: (logId: string) => void;
}

export default function ListErrorLogs({ errorLogs, onViewDetail }: Readonly<IProps>) {
    return (
        <div className="table-responsive">
            <table className="table table-lg align-middle mb-0">
                <thead>
                    <tr>
                        <th>Timestamp</th>
                        <th>error category</th>
                        <th>system</th>
                        <th>API endpoint</th>
                        <th>error message</th>
                        <th className="text-center">Action</th>
                    </tr>
                </thead>
                <tbody>
                    {errorLogs.map((log) => (
                        <tr key={log.id!}>
                            <td>{formatDateTime(log.created_at, FORMAT_DATE.SHOW_DATE_MINUTE)}</td>
                            <td>
                                {errorCategoryNames.find((item) => item.id === log.error_category)?.name ?? 'Unknown'}
                            </td>
                            <td>{SystemNames.find((item) => item.id === log.system)?.name ?? 'Unknown'}</td>
                            <td>{log.api_endpoint}</td>
                            <td>{log.error_message}</td>
                            <td className="text-center">
                                <button
                                    type="button"
                                    className="btn btn-sm btn-icon btn-flat-info waves-effect"
                                    title="View Details"
                                    onClick={() => onViewDetail(log.id!)}
                                >
                                    <Eye size={14} />
                                </button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
