import React from 'react';

interface NotInterestedIconProps extends React.SVGProps<SVGSVGElement> {
    size?: number | string;
    className?: string;
}

const NotInterestedIcon: React.FC<NotInterestedIconProps> = ({ size = 24, className = '', ...props }) => (
    <svg
        className={`svg-icon ${className}`}
        style={{
            width: typeof size === 'number' ? `${size}px` : size,
            height: typeof size === 'number' ? `${size}px` : size,
            verticalAlign: 'middle',
            fill: 'currentColor',
            overflow: 'hidden',
            ...props.style,
        }}
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path d="M512 85.333333C276.266667 85.333333 85.333333 276.266667 85.333333 512s190.933333 426.666667 426.666667 426.666667 426.666667-190.933333 426.666667-426.666667S747.733333 85.333333 512 85.333333z m0 768c-188.586667 0-341.333333-152.746667-341.333333-341.333333 0-78.933333 27.093333-151.253333 71.893333-209.066667L721.066667 781.44C663.253333 826.24 590.933333 853.333333 512 853.333333z m269.44-132.266666L302.933333 242.56C360.746667 197.76 433.066667 170.666667 512 170.666667c188.586667 0 341.333333 152.746667 341.333333 341.333333 0 78.933333-27.093333 151.253333-71.893333 209.066667z" />
    </svg>
);

export default NotInterestedIcon;
