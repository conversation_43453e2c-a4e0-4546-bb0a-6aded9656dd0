import { useAuthStore } from '../../../stores/authStore';
import { ChangeEvent, useMemo, useState } from 'react';
import { AuthGroups } from '../../../types/User';
import { Helmet } from 'react-helmet-async';
import ContentHeader from '../../../components/partials/ContentHeader';
import { ErrorLogsSearchForm, ErrorLogsStatistical, ListErrorLogs } from '../components/errorLogs';
import PaginationTable from '../../../components/partials/PaginationTable';
import useQueryParams from '../../../hooks/useQueryParams';
import {
    ErrorLog,
    ErrorLogDashboard,
    errorLogFilterConfig,
    ErrorLogQuery,
    SearchErrorLogParam,
} from '../../../types/Logs';
import { PAGINATION, QUERY_KEY } from '../../../constants/common';
import { convertDateRangeToQueryParams } from '../../../utils/date';
import OmitBy from 'lodash/omitBy';
import isUndefined from 'lodash/isUndefined';
import { generateFilters, showToast } from '../../../utils/common';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { BaseSearch } from '../../../types/common';
import { ERROR_LOG_DASHBOARD, ERROR_LOG_DETAIL, ERROR_LOGS_LIST, exportErrorLog } from '../../../services/LogService';
import { keepPreviousData } from '@tanstack/react-query';
import Spinner from '../../../components/partials/Spinner';
import { HttpStatusCode } from 'axios';
import ErrorLogDetailModal from '../components/errorLogs/ErrorLogDetailModal';

export default function ErrorLogs() {
    const user = useAuthStore((state) => state.user);
    const isSuperAdmin = useMemo(() => user?.auth_group === AuthGroups.SUPER_ADMIN, [user?.auth_group]);
    const [showDetailModal, setShowDetailModal] = useState(false);
    const [selectedLogId, setSelectedLogId] = useState('');

    const { queryParams, setQueryParams } = useQueryParams<SearchErrorLogParam>();
    const paramConfig: SearchErrorLogParam = OmitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            created_at_from:
                convertDateRangeToQueryParams(queryParams.created_at__range || '')?.created_at_from ?? undefined,
            created_at_to:
                convertDateRangeToQueryParams(queryParams.created_at__range || '')?.created_at_to ?? undefined,
            error_category: queryParams.error_category,
            error_message: queryParams.error_message,
            system: queryParams.system,
        },
        isUndefined
    );

    const { limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, errorLogFilterConfig);

    const { data, isLoading, isRefetching } = useGraphQLQuery<ErrorLogQuery, BaseSearch>(
        [QUERY_KEY.ERROR_LOGS, queryParams],
        ERROR_LOGS_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            filters: filters.length > 0 ? filters : undefined,
            sort: undefined,
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );
    const errorLogs = useMemo(() => data?.error_logs_list?.data ?? [], [data]);

    const handleExport = async () => {
        const res = await exportErrorLog({ search: '', filters, sort: paramConfig.sort });
        if (res.status === HttpStatusCode.Created) {
            showToast(true, ['Export successfully']);
            const blob = new Blob([res.data], { type: res.headers['content-type'] });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `error-logs-${new Date().toISOString()}.xlsx`;
            a.click();
            window.URL.revokeObjectURL(url);
        }
    };

    const handlePageChange = (_event: ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const handleViewDetail = (logId: string) => {
        setSelectedLogId(logId);
        setShowDetailModal(true);
    };

    const { data: errorLogDashboardData } = useGraphQLQuery<{ error_log_dashboard: ErrorLogDashboard }>(
        [QUERY_KEY.ERROR_LOG_DASHBOARD],
        ERROR_LOG_DASHBOARD,
        undefined,
        '',
        {
            placeholderData: keepPreviousData,
        }
    );
    const errorLogDashboard = useMemo(() => errorLogDashboardData?.error_log_dashboard, [errorLogDashboardData]);

    const handleSearchOnDashboard = (errorCategories: string | null) => {
        setQueryParams({
            error_category: errorCategories ?? '',
            page: '1',
        });
    };

    const { data: errorLogDetailData } = useGraphQLQuery<{ error_log_detail: ErrorLog }, { id: string }>(
        [QUERY_KEY.ERROR_LOG, selectedLogId],
        ERROR_LOG_DETAIL,
        {
            id: selectedLogId,
        },
        '',
        {
            enabled: !!selectedLogId,
            placeholderData: keepPreviousData,
        }
    );
    const errorLogDetail = useMemo(() => errorLogDetailData?.error_log_detail, [errorLogDetailData]);

    return (
        <>
            <Helmet>
                <title>{'Error Log'}</title>
            </Helmet>
            <ContentHeader
                title={'Error Log'}
                contextMenu={
                    isSuperAdmin
                        ? [
                              {
                                  text: 'Export To Excel',
                                  to: ``,
                                  icon: 'DOWNLOAD',
                                  fnCallBack: { actionMenu: handleExport },
                              },
                          ]
                        : []
                }
            />
            <div className="content-body">
                <div className="row">
                    <div className="col-8">
                        <ErrorLogsStatistical
                            errorLogDashboard={errorLogDashboard}
                            handleSearchOnDashboard={handleSearchOnDashboard}
                        />
                    </div>
                    <div className="col-12">
                        <ErrorLogsSearchForm isLoading={isLoading} isRefetching={isRefetching} />
                    </div>
                    <div className="col-12">
                        <div className="card">
                            {isLoading || (isRefetching && <Spinner />)}
                            {!isLoading && !isRefetching && (
                                <>
                                    <ListErrorLogs errorLogs={errorLogs} onViewDetail={handleViewDetail} />
                                    <PaginationTable
                                        countItem={data?.error_logs_list?.totalCount || 0}
                                        totalPage={data?.error_logs_list?.totalPages || 0}
                                        currentPage={data?.error_logs_list?.currentPage || 1}
                                        handlePageChange={handlePageChange}
                                    />
                                </>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            <ErrorLogDetailModal show={showDetailModal} changeShow={setShowDetailModal} errorLog={errorLogDetail} />
        </>
    );
}
