import { useState } from 'react';
import { User, Trash2, Send } from 'react-feather';
import { FORMAT_DATE, formatDateTime } from '../../../utils/date';
import TaskComment, { ITaskCommentGroup } from '../../../types/TaskComment';
import { Task } from '../../../types/Task';
import ModalConfirm from '../../../components/partials/ModalConfirm';
import { useAuthStore } from '../../../stores/authStore';

interface IProps {
    taskComments: TaskComment[];
    onSendComment?: (content: string) => void;
    onDeleteComment?: (id: string) => void;
    isSendingComment?: boolean;
    userTaskDetail?: Task;
    groupedTaskComments: ITaskCommentGroup[];
    isEdit: boolean;
}

export default function TaskCommentsSection({
    taskComments,
    onSendComment,
    onDeleteComment,
    isSendingComment,
    userTaskDetail,
    groupedTaskComments,
    isEdit,
}: Readonly<IProps>) {
    const [commentInput, setCommentInput] = useState('');
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
    const [commentToDelete, setCommentToDelete] = useState<string | null>(null);
    const currentUser = useAuthStore((state) => state.user);

    const handleSend = () => {
        if (commentInput.trim() && onSendComment) {
            onSendComment(commentInput.trim());
            setCommentInput('');
        }
    };

    const handleDelete = (id: string) => {
        setCommentToDelete(id);
        setShowDeleteConfirm(true);
    };

    const handleConfirmDelete = () => {
        if (commentToDelete && onDeleteComment) {
            onDeleteComment(commentToDelete);
        }
        setShowDeleteConfirm(false);
        setCommentToDelete(null);
    };

    const handleCancelDelete = () => {
        setShowDeleteConfirm(false);
        setCommentToDelete(null);
    };

    return (
        <div className="tw-flex tw-flex-col tw-h-[60vh] tw-max-h-[60vh]">
            <div className="tw-flex-1 tw-overflow-y-auto tw-pr-2 tw-space-y-4">
                {taskComments && taskComments.length > 0 ? (
                    <>
                        {groupedTaskComments.map((item, idx) => (
                            <div key={item.workflow_step_id}>
                                <div className="tw-text-lg tw-font-bold tw-mt-4 tw-text-center tw-flex tw-items-center tw-gap-2">
                                    <div className="tw-flex-1 tw-border-t tw-border-gray-300"></div>
                                    <span className="tw-px-2">{item.workflow_step_name}</span>
                                    <div className="tw-flex-1 tw-border-t tw-border-gray-300"></div>
                                </div>
                                {item?.comments?.map((comment, cIdx) => {
                                    const roles = comment?.userTask?.userRoles?.map?.((role) => role.name).join(', ');
                                    return (
                                        <div className="tw-flex tw-items-start tw-gap-2 mb-2" key={comment.id}>
                                            <div className="tw-flex-shrink-0">
                                                <div className="tw-bg-gray-200 tw-rounded-full tw-w-8 tw-h-8 tw-flex tw-items-center tw-justify-center">
                                                    <User size={18} className="tw-text-gray-500" />
                                                </div>
                                            </div>
                                            <div className="tw-flex-1">
                                                <div className="tw-flex tw-items-center tw-gap-2">
                                                    <span className="tw-font-bold">
                                                        {comment.creator.full_name} {roles ? `(${roles})` : ''}
                                                    </span>
                                                    <span className="tw-text-xs tw-text-gray-400 tw-ml-2">
                                                        {formatDateTime(
                                                            comment.created_at!,
                                                            FORMAT_DATE.SHOW_DATE_MINUTE
                                                        )}
                                                    </span>
                                                    {currentUser?.id === comment.created_by && isEdit && (
                                                        <button
                                                            className="tw-ml-auto tw-text-gray-400 hover:tw-text-red-500 tw-p-1 tw-bg-transparent tw-border-none"
                                                            onClick={() => handleDelete(comment.id)}
                                                        >
                                                            <Trash2 size={16} />
                                                        </button>
                                                    )}
                                                </div>
                                                <div className="tw-bg-gray-100 tw-rounded tw-p-3 tw-mt-1 tw-text-sm">
                                                    {comment.content}
                                                </div>
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        ))}
                    </>
                ) : (
                    <div className="tw-text-center tw-text-gray-400 tw-mt-8">No comments yet.</div>
                )}
            </div>
            {isEdit && (
                <div className="tw-mt-4 tw-flex tw-items-end tw-border tw-rounded tw-p-2">
                    <textarea
                        className="tw-flex-1 tw-border-none tw-outline-none tw-bg-transparent tw-resize-none tw-min-h-[40px] tw-max-h-[120px]"
                        placeholder="Comment here"
                        maxLength={2000}
                        value={commentInput}
                        onChange={(e) => setCommentInput(e.target.value)}
                        rows={2}
                    />
                    <button
                        className="tw-ml-2 tw-text-primary tw-bg-transparent tw-border-none"
                        disabled={!commentInput.trim() || isSendingComment}
                        onClick={handleSend}
                    >
                        <Send size={20} />
                    </button>
                </div>
            )}
            <ModalConfirm
                show={showDeleteConfirm}
                text="Are you sure you want to delete this comment? This action cannot be undone."
                btnDisabled={false}
                changeShow={handleCancelDelete}
                submitAction={handleConfirmDelete}
                textTitle="Delete Comment"
                isDelete={true}
            />
        </div>
    );
}
