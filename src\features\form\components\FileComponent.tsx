import { useEffect, useRef, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import FileService, { FILE_DETAIL } from 'services/FileService';
import { Download, FileText, X } from 'react-feather';
import { FileDetailQuery, ItemFile } from 'types/common/Item';
import { FORM_FILE_ACCEPT, QUERY_KEY } from '../../../constants/common';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { keepPreviousData } from '@tanstack/react-query';

interface FileComponentProps {
    field: {
        name: string;
        value: any;
        onChange: (...event: any[]) => void;
        onBlur: () => void;
        ref?: React.Ref<any>;
    };
    disabled: boolean;
    isFileEntry?: boolean;
}

const MAX_SIZE_MB = 10;
const ALLOWED_TYPES = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
];

export default function FileComponent({ field, disabled, isFileEntry }: FileComponentProps) {
    const { setValue } = useFormContext();
    const fileInputRef = useRef<HTMLInputElement | null>(null);
    const [uploading, setUploading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [uploadedFiles, setUploadedFiles] = useState<ItemFile[]>([]);
    const isChangeRequestViewRoute = location.pathname.startsWith('/changeRequest/view');
    const isWorkflowInstanceRoute = location.pathname.startsWith('/workflowInstance');
    const isTaskViewRoute = location.pathname.startsWith('/task/view');

    const { data: fileData } = useGraphQLQuery<FileDetailQuery>(
        [QUERY_KEY.FILE_DETAIL, field.value],
        FILE_DETAIL,
        { ids: Array.isArray(field.value) ? field.value : field.value ? [field.value] : [] },
        '',
        {
            enabled: !!field.value && (Array.isArray(field.value) ? field.value.length > 0 : true),
            placeholderData: keepPreviousData,
        }
    );

    useEffect(() => {
        if (fileData && fileData.file_detail) {
            const files = fileData.file_detail.map((file) => ({
                id: file.id,
                name: file.file_name,
                url: file.file_url,
                fileSize: file.file_size,
                mineType: file.mime_type,
            }));
            setUploadedFiles(files);
        }
    }, [fileData]);

    const handleUpload = async (files: FileList) => {
        if (!files || files.length === 0) return;

        const validFiles: File[] = [];
        const errors: string[] = [];

        // Validate each file
        Array.from(files).forEach((file, index) => {
            // Validate file type
            if (!ALLOWED_TYPES.includes(file.type)) {
                errors.push(`File ${index + 1}: Invalid file type. Please upload PDF, Word, or Excel files.`);
                return;
            }

            // Validate file size
            if (file.size > MAX_SIZE_MB * 1024 * 1024) {
                errors.push(`File ${index + 1}: File size must be less than 10MB.`);
                return;
            }

            validFiles.push(file);
        });

        if (errors.length > 0) {
            setError(errors.join(' '));
            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }
            return;
        }

        setUploading(true);

        try {
            const uploadPromises = validFiles.map((file) => FileService.upload(file));
            const results = await Promise.all(uploadPromises);

            const newUploadedFiles: ItemFile[] = results.map((res, index) => ({
                id: res.upload.id,
                name: res.upload.name,
                url: res.upload.url,
                fileSize: validFiles[index].size,
            }));

            const currentFileIds = Array.isArray(field.value) ? field.value : [];
            const newFileIds = results.map((res) => res.upload.id);
            const allFileIds = [...currentFileIds, ...newFileIds];

            setValue(field.name, allFileIds);
            setUploadedFiles((prev) => [...prev, ...newUploadedFiles]);
            setError(null);

            // Clear the file input after successful upload
            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }
        } catch (err) {
            setError('Upload failed. Please try again.');
            // Clear the file input on upload failure
            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }
        } finally {
            setUploading(false);
        }
    };

    const handleRemoveFile = (fileId: string | number) => {
        const currentFileIds = Array.isArray(field.value) ? field.value : [];
        const updatedFileIds = currentFileIds.filter((id) => id !== fileId);
        const updatedFiles = uploadedFiles.filter((file) => file.id !== fileId);

        setValue(field.name, updatedFileIds);
        setUploadedFiles(updatedFiles);
        setError(null);
    };

    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const handleFileSelect = () => {
        if (fileInputRef.current) {
            fileInputRef.current.click();
        }
    };

    return (
        <div className="tw-flex tw-flex-col tw-gap-3">
            {!isChangeRequestViewRoute && !isWorkflowInstanceRoute && !isTaskViewRoute && !isFileEntry && (
                <>
                    {/* Hidden file input */}
                    <input
                        accept={FORM_FILE_ACCEPT}
                        type="file"
                        multiple
                        ref={fileInputRef}
                        onChange={(e) => {
                            const files = e.target.files;
                            if (files && files.length > 0) {
                                // Clear any previous errors
                                setError(null);
                                handleUpload(files);
                            } else {
                                if (!uploadedFiles.length) {
                                    field.onChange([]);
                                }
                            }
                        }}
                        style={{ display: 'none' }}
                        disabled={uploading}
                    />

                    {/* Custom file upload UI - mimicking browser default */}
                    <div className="tw-relative tw-border tw-border-gray-300 tw-rounded tw-bg-white tw-flex tw-items-center tw-min-h-[38px]">
                        <button
                            type="button"
                            className="tw-px-3 tw-py-2 tw-bg-white tw-border-r tw-border-gray-300 tw-text-sm tw-text-gray-700 hover:tw-bg-gray-50 tw-transition-colors"
                            onClick={handleFileSelect}
                            disabled={disabled || uploading}
                            style={{
                                pointerEvents: disabled ? 'none' : 'auto',
                            }}
                        >
                            Choose Files
                        </button>
                        <div className="tw-flex-1 tw-px-3 tw-py-2 tw-text-sm tw-text-gray-500">
                            Please upload PDF, Word, or Excel files (max 10MB). You can select multiple files.
                        </div>
                    </div>
                    {uploading && <div className="tw-text-sm tw-text-blue-600">Uploading...</div>}
                </>
            )}
            {error && <div className="tw-text-sm tw-text-red-600">{error}</div>}

            {/* Display uploaded files info */}
            {uploadedFiles.length > 0 && (
                <div className="tw-flex tw-flex-col tw-gap-2">
                    {uploadedFiles.map((file) => (
                        <div
                            key={file.id}
                            className="tw-border tw-border-gray-300 tw-rounded tw-p-3 tw-flex tw-items-center tw-justify-between tw-bg-gray-50 tw-min-w-0"
                        >
                            <div className="tw-flex tw-items-center tw-gap-2 tw-min-w-0 tw-flex-1">
                                <FileText size={16} className="tw-text-gray-600 tw-flex-shrink-0" />
                                <div className="tw-min-w-0 tw-flex-1">
                                    <div
                                        className="tw-text-sm tw-font-medium tw-truncate tw-max-w-full"
                                        title={file.name}
                                    >
                                        {file.name}
                                    </div>
                                    <div className="tw-text-xs tw-text-gray-500">
                                        {file.fileSize && formatFileSize(file.fileSize)}
                                    </div>
                                </div>
                            </div>
                            <div className="tw-flex tw-items-center tw-gap-2 tw-flex-shrink-0 tw-ml-2">
                                <a
                                    href={file.url}
                                    download={file.name}
                                    className="tw-text-blue-600 hover:tw-text-blue-800 tw-transition-colors"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    <Download size={16} />
                                </a>
                                {!isChangeRequestViewRoute &&
                                    !isWorkflowInstanceRoute &&
                                    !isTaskViewRoute &&
                                    !isFileEntry && (
                                        <button
                                            type="button"
                                            className="tw-text-red-500 hover:tw-text-red-700 tw-transition-colors"
                                            onClick={() => handleRemoveFile(file.id)}
                                            disabled={disabled}
                                            title="Remove file"
                                        >
                                            <X size={16} />
                                        </button>
                                    )}
                            </div>
                        </div>
                    ))}
                </div>
            )}
            {(!field?.value || (Array.isArray(field.value) && field.value.length === 0)) &&
                (isChangeRequestViewRoute || isWorkflowInstanceRoute || isTaskViewRoute || isFileEntry) && (
                    <div className="tw-border tw-border-gray-300 tw-rounded tw-p-3 tw-flex tw-items-center tw-justify-between tw-bg-gray-50 tw-min-w-0">
                        <div className="tw-flex tw-items-center tw-gap-2 tw-min-w-0 tw-flex-1">
                            <FileText size={16} className="tw-text-gray-600 tw-flex-shrink-0" />
                            <div className="tw-min-w-0 tw-flex-1">
                                <div className="tw-text-sm tw-font-medium tw-truncate tw-max-w-full" title="Not file">
                                    Not file
                                </div>
                                <div className="tw-text-xs tw-text-gray-500">0 KB</div>
                            </div>
                        </div>
                        <div className="tw-text-red-500 hover:tw-cursor-not-allowed tw-transition-colors tw-flex-shrink-0 tw-ml-2">
                            <Download size={16} />
                        </div>
                    </div>
                )}
        </div>
    );
}
