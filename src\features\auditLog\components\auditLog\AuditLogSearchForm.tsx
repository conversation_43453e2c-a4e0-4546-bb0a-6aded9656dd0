import SearchForm from '../../../../components/partials/SearchForm';
import { AuditLogActionTypeNames } from '../../../../types/Logs';

interface IProps {
    isLoading: boolean;
    isRefetching: boolean;
}

export default function AuditLogSearchForm({ isLoading, isRefetching }: Readonly<IProps>) {
    return (
        <SearchForm
            fields={[
                {
                    name: 'search',
                    type: 'text',
                    label: 'Keyword',
                    wrapClassName: 'col-md-3 col-12',
                    placeholder: 'Search by user name, MOC request title',
                },
                {
                    name: 'created_at__range',
                    type: 'date-range',
                    label: 'Timestamp Range',
                    wrapClassName: 'col-md-3 col-12',
                },
                {
                    name: 'action_type',
                    type: 'select',
                    label: 'Action Type',
                    wrapClassName: 'col-md-3 col-12',
                    options: {
                        multiple: true,
                        choices: AuditLogActionTypeNames.map((item) => ({
                            label: item.name,
                            value: item.id,
                        })),
                    },
                },
            ]}
            isLoading={isLoading || isRefetching}
        />
    );
}
