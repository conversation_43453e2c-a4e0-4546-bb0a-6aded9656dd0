import { useEffect, useRef, useState } from 'react';
import { Control, Controller, useFormContext } from 'react-hook-form';
import classNames from 'classnames';
import { ChevronDown, ChevronRight } from 'react-feather';
import { useAppStore } from 'stores/appStore';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import Area, { AreaQueryRes, AreaType } from 'types/OperationalArea';
import { QUERY_KEY } from 'constants/common';
import { AREAS_LIST } from 'services/AreaService';
import { keepPreviousData } from '@tanstack/react-query';

const getAllDescendantIds = (node: any): string[] => {
    if (!node.children) return [];
    return node.children.flatMap((child: any) => [child.id, ...getAllDescendantIds(child)]);
};

const findNodeById = (id: string, nodes: any[]): any | null => {
    for (const node of nodes) {
        if (node.id === id) return node;
        if (node.children) {
            const found = findNodeById(id, node.children);
            if (found) return found;
        }
    }
    return null;
};

const buildLevel1Map = (
    nodes: any[],
    parentId: string | null = null,
    level1Id: string | null = null
): Map<string, string> => {
    const map = new Map<string, string>();
    for (const node of nodes) {
        const currentLevel1Id = parentId === null ? node.id : level1Id;
        map.set(node.id, currentLevel1Id!);
        if (node.children) {
            const childMap = buildLevel1Map(node.children, node.id, currentLevel1Id);
            childMap.forEach((val, key) => map.set(key, val));
        }
    }
    return map;
};

interface PlanAndAreaUnitProps {
    name: string;
    control: Control<any, any>;
    disabled?: boolean;
}

export default function PlanAndAreaUnit({ name, disabled = false }: PlanAndAreaUnitProps) {
    const areaList = useAppStore((state) => state.areaList);
    const setAreaList = useAppStore((state) => state.setAreaList);
    const { control } = useFormContext();
    const level1Map = buildLevel1Map(areaList);

    const { data: dataArea } = useGraphQLQuery<AreaQueryRes>(
        [QUERY_KEY.AREAS, AreaType.ZONE],
        AREAS_LIST,
        {
            filters: [`type:=(${AreaType.ZONE})`],
            sort: 'created_at:ASC',
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    useEffect(() => {
        const areaList: Area[] = dataArea?.areas_list || [];
        setAreaList(areaList);
    }, [dataArea, setAreaList]);

    const getAncestorIds = (id: string, nodes: any[], path: string[] = []): string[] => {
        for (const node of nodes) {
            if (node.id === id) return [...path];
            if (node.children) {
                const childPath = getAncestorIds(id, node.children, [...path, node.id]);
                if (childPath.length) return childPath;
            }
        }
        return [];
    };

    const filterTree = (nodes: any[], keyword: string): any[] =>
        nodes
            .map((node) => {
                const children = node.children ? filterTree(node.children, keyword) : [];
                const matched = node?.name.toLowerCase().includes(keyword.toLowerCase());
                if (matched || children.length > 0) {
                    return {
                        ...node,
                        children: children.length > 0 ? children : undefined,
                    };
                }
                return null;
            })
            .filter(Boolean);

    return (
        <Controller
            control={control}
            name={name}
            defaultValue={{}}
            render={({ field: { value = {}, onChange } }) => {
                const flatChecked: any = Object.values(value).flat();

                const handleToggle = (id: string, checked: boolean) => {
                    const node = findNodeById(id, areaList);
                    if (!node) return;

                    const checkedSet = new Set(flatChecked);
                    const childIds = getAllDescendantIds(node);

                    if (checked) {
                        checkedSet.add(id);
                        childIds.forEach((cid) => checkedSet.add(cid));
                    } else {
                        checkedSet.delete(id);
                        childIds.forEach((cid) => checkedSet.delete(cid));
                    }

                    const expandedSet: any = new Set(checkedSet);
                    [...checkedSet].forEach((id: any) => {
                        const ancestors = getAncestorIds(id, areaList);
                        ancestors.forEach((aid) => expandedSet.add(aid));
                    });

                    const updateAllParentStates = (nodes: any[]) => {
                        for (const node of nodes) {
                            if (node.children?.length) {
                                updateAllParentStates(node.children);
                                const allChecked = node.children.every((c: any) => expandedSet.has(c.id));
                                const someChecked = node.children.some((c: any) => expandedSet.has(c.id));
                                if (allChecked) expandedSet.add(node.id);
                                else if (!someChecked) expandedSet.delete(node.id);
                            }
                        }
                    };

                    updateAllParentStates(areaList);

                    const grouped: Record<string, string[]> = {};
                    [...expandedSet].forEach((id: any) => {
                        const root = level1Map.get(id);
                        if (root) {
                            if (!grouped[root]) grouped[root] = [];
                            grouped[root].push(id);
                        }
                    });

                    onChange(grouped);
                };

                return (
                    <div className="tw-max-h-[350px] tw-overflow-auto tw-border tw-border-[#d8d6de] tw-rounded-md">
                        {areaList.length === 0 && <div className="tw-text-gray-400 tw-text-sm">Data entry...</div>}
                        {areaList.map((node) => (
                            <TreeNode
                                key={node.id}
                                node={node}
                                checked={flatChecked}
                                onToggle={handleToggle}
                                disabled={disabled}
                            />
                        ))}
                    </div>
                );
            }}
        />
    );
}

const TreeNode = ({
    node,
    checked,
    onToggle,
    depth = 1,
    disabled = false,
}: {
    node: any;
    checked: string[];
    onToggle: (id: string, checked: boolean) => void;
    depth?: number;
    disabled: boolean;
}) => {
    const [expanded, setExpanded] = useState(true);
    const checkboxRef = useRef<HTMLInputElement | null>(null);

    const childIds = node.children?.map((c: any) => c.id) || [];
    const checkedCount = childIds.filter((id: any) => checked.includes(id)).length;
    const isChecked = checked.includes(node.id);
    const isIndeterminate = checkedCount > 0 && checkedCount < childIds.length;

    useEffect(() => {
        if (checkboxRef.current) {
            checkboxRef.current.indeterminate = isIndeterminate;
        }
    }, [isIndeterminate]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        onToggle(node.id, e.target.checked);
    };

    return (
        <div
            className={classNames(
                'tw-ml-[20px]',

                {
                    'tw-ml-[0px]': depth === 1,
                }
            )}
        >
            <div className="tw-flex tw-mb-[4px]">
                {node.children && (
                    <div onClick={() => setExpanded(!expanded)} style={{ marginRight: 4, cursor: 'pointer' }}>
                        {expanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                    </div>
                )}
                <div
                    className={classNames('', {
                        'form-check': depth >= 3,
                    })}
                >
                    {depth >= 3 && (
                        <input
                            className="form-check-input"
                            ref={checkboxRef}
                            type="checkbox"
                            checked={isChecked}
                            onChange={handleChange}
                            id={node.id}
                            disabled={disabled}
                        />
                    )}
                    <label className="form-check-label" htmlFor={node.id}>
                        {node.name}
                    </label>
                </div>
            </div>

            {expanded &&
                node.children?.map((child: any) => (
                    <TreeNode
                        key={child.id}
                        node={child}
                        checked={checked}
                        onToggle={onToggle}
                        depth={depth + 1}
                        disabled={disabled}
                    />
                ))}
        </div>
    );
};
