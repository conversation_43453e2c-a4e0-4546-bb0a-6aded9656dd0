import { <PERSON>Model, BaseModelString, <PERSON><PERSON>earch, DataList, FilterConfig } from './common';
import { FILTER_CONDITIONS } from '../constants/common';
import { TaskStatus } from './Task';
import { IFile } from './common/Item';
import { FormItem } from './Form';
import { UserAccount } from './User';
import Area from './OperationalArea';

export enum WorkflowStatus {
    DRAFT = 'DRAFT',
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
}

export enum WorkflowInstaceType {
    TEMPORARY = 'TEMPORARY',
    PERMANENT = 'PERMANENT',
}

export interface WorkflowListQueryVariables {
    page?: number;
    limit?: number;
    search?: string;
    sort?: string;
    filters?: string[];
}

export interface WorkflowDefinition extends BaseModel {
    name: string;
    bpmnXml: string;
    status: WorkflowStatus;
    camunda_key: string;
    camunda_id: string;
    version: string;
    workflow_definition: WorkflowDefinition;
    workflow_steps: WorkflowStepsType[];
    workflow_forms: WorkflowFormType[];
}

export interface WorkflowQuery extends DataList<null> {
    workflow_definition_list_paginate: DataList<WorkflowDefinition>;
}
export interface WorkflowDefinitionListQuery {
    workflow_definition_list_all: WorkflowInstance[];
}

export interface WorkflowInstance extends BaseModelString {
    process_instance_id: string;
    camunda_key: string;
    business_key: string;
    name: string;
    status: WorkflowInstanceStatus;
    started_at: string | null;
    ended_at: string | null;
    form_data: any;
    camunda_variables: any;
    current_user_task_id: string;
    current_user_task: UserTaskEntity;
    workflow_definition: WorkflowDefinition;
    workflow_steps: WorkflowStepsType[];
    workflow_forms: WorkflowFormType[];
    user_tasks: UserTaskEntity[];
    files: IFile[];
    type: WorkflowInstaceType;
    tracking_status: TrackingStatus;
    sub_area_ids: string;
    area: Area;
    workflow_instance_steps: WorkflowInstanceStep[];
}

export interface UserTaskEntity extends BaseModelString {
    workflow_instance_id: string;
    task_id: string;
    task_key: string;
    task_name: string;
    assignee: string;
    status: TaskStatus;
    started_at: string;
    completed_at: string | null;
    completed_by: string | null;
    form_id: string;
    form_data: Record<string, any> | null;
    variables: Record<string, any> | null;
    workflow_step_id: string;
    workflow_step: WorkflowStepsType;
    files: IFile[];
    order: number;
    element_variable: string;
    element_value: string;
    subprocess_element_variable: string;
    subprocess_element_value: string;
    form: FormEntity;
    assigneeInfo: UserAccount;
    workflow_instance: WorkflowInstance;
    userRoles: UserRolesType[];
    user_zones: UserZoneType[];
}

export interface UserRolesType extends BaseModelString {
    is_gatekeeper_child: string;
    type: string;
    name: string;
    variable_name: string;
}
export interface UserZoneType extends BaseModelString {
    parent_area_id: string;
    type: string;
    name: string;
    code: string;
    out_of_service: boolean;
}

export interface WorkflowFormType extends BaseModelString {
    id: string;
    form_id: string;
    task_key: string;
    task_name: string;
    is_first_form: boolean;
    workflow_definition: WorkflowDefinition;
    form: FormEntity;
}

export interface FormEntity extends BaseModelString {
    id: string;
    created_by?: string;
    updated_by?: string;
    created_at: string;
    updated_at?: string;
    deleted_at?: string;
    name: string;
    step?: string;
    schema: FormItem;
    description?: string;
}
export interface WorkflowStepsType {
    id: string;
    step_key: string;
    step_name: string;
    workflow_definition: WorkflowDefinition;
    step_order: number;
}

export interface WorkflowInstanceQuery {
    workflow_instances_list: DataList<WorkflowInstance>;
}

export interface WorkflowListQuery {
    getAllWorkflows: DataList<WorkflowInstance>;
}

export interface WorkflowInstanceDetailQuery {
    workflow_instances_detail: WorkflowInstance;
}

export interface SearchWorkflow extends BaseSearch {
    status?: string;
    version?: string;
}

export type SearchWorkflowParam = {
    [key in keyof SearchWorkflow]: string;
};

export const workflowFilterConfig: FilterConfig = {
    status: { key: 'status', operator: FILTER_CONDITIONS.IN },
    version: { key: 'version', operator: FILTER_CONDITIONS.EQUAL },
};

export interface SearchWorkflowInstance extends BaseSearch {
    status_id?: string;
    created_at_from?: string;
    created_at_to?: string;
    created_at__range?: string;
    created_by?: string;
    current_status?: string;
    not_draft?: string;
    type?: string;
    tracking_status?: string;
    sub_area_id?: string;
    sort?: string;
}

export type SearchWorkflowInstanceParam = {
    [key in keyof SearchWorkflowInstance]: string;
};

export const workflowInstanceFilterConfig: FilterConfig = {
    status_id: { key: 'status', operator: FILTER_CONDITIONS.IN },
    created_at_from: { key: 'created_at', operator: FILTER_CONDITIONS.GREATER_OR_EQUAL },
    created_at_to: { key: 'created_at', operator: FILTER_CONDITIONS.LESS_OR_EQUAL },
    created_by: { key: 'created_by', operator: FILTER_CONDITIONS.EQUAL },
    current_status: { key: 'current_user_task.status', operator: FILTER_CONDITIONS.IN },
    type: { key: 'type', operator: FILTER_CONDITIONS.EQUAL },
    tracking_status: { key: 'tracking_status', operator: FILTER_CONDITIONS.IN },
    sub_area_id: { key: 'sub_area_ids', operator: FILTER_CONDITIONS.IN },
};

export interface WorkflowDetailQuery {
    workflow_definition_detail: WorkflowDefinition;
}

export interface UpdateProcessResponse {
    updateProcess: WorkflowDefinition;
}

export interface ItemParam {
    id: string;
    name: string;
}

export const WorkflowStatusNames: ItemParam[] = [
    { id: WorkflowStatus.DRAFT, name: 'Draft' },
    { id: WorkflowStatus.ACTIVE, name: 'Active' },
    { id: WorkflowStatus.INACTIVE, name: 'Inactive' },
];

export enum WorkflowInstanceStatus {
    DRAFT = 'DRAFT',
    IN_PROGRESS = 'IN_PROGRESS',
    COMPLETED = 'COMPLETED',
    TERMINATED = 'TERMINATED',
}

export const WorkflowInstanceStatusName = [
    { id: WorkflowInstanceStatus.DRAFT, name: 'draft' },
    { id: WorkflowInstanceStatus.IN_PROGRESS, name: 'in-progress' },
    { id: WorkflowInstanceStatus.COMPLETED, name: 'completed' },
    { id: WorkflowInstanceStatus.TERMINATED, name: 'terminated' },
];

export interface ChangeRequestQueryRes {
    change_request_list: DataList<WorkflowInstance>;
}

export const workflowInstanceTypeNames = [
    { id: WorkflowInstaceType.TEMPORARY, name: 'Temporary' },
    { id: WorkflowInstaceType.PERMANENT, name: 'Permanent' },
];

export enum TrackingStatus {
    ON_TRACKING = 'on_tracking',
    COMPLETED = 'completed',
    CANCELLED = 'cancelled',
    OVERDUE = 'overdue',
    BACKLOG = 'backlog',
    DRAFT = 'draft',
}

export const TrackingStatusNames = [
    { id: TrackingStatus.ON_TRACKING, name: 'On Tracking' },
    { id: TrackingStatus.COMPLETED, name: 'Completed' },
    { id: TrackingStatus.CANCELLED, name: 'Cancelled' },
    { id: TrackingStatus.OVERDUE, name: 'Overdue' },
    { id: TrackingStatus.BACKLOG, name: 'Backlog' },
];

export interface ChangeRequestDashboard {
    in_progress: number;
    completed: number;
    cancelled: number;
    overdue: number;
    backlog: number;
    draft: number;
}

export interface WorkflowInstanceStep extends BaseModelString {
    workflow_instance_id: string;
    workflow_step_id: string;
    start_at: string;
    due_date: string;
    completed_at: string;
    workflow_step: WorkflowStepsType;
}
