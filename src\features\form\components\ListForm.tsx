import { Paging } from 'types/common';
import { FORMAT_DATE, formatDateTime } from 'utils/date';
import { Link } from 'react-router-dom';
import { FormQueryItem, FormStatus } from 'types/Form';
import { genTableIndex } from 'utils/common';
import { Edit, Power, Trash2 } from 'react-feather';

interface IProps {
    items: FormQueryItem[];
    paging: Paging;
    handleDelete?: (id: string) => void;
    handleActive?: (item: FormQueryItem) => void;
    handleInActive?: (item: FormQueryItem) => void;
    isSuperAdmin: boolean;
}

export const getStatusBadge = (status: FormStatus) => {
    switch (status) {
        case FormStatus.DRAFT:
            return <span className="badge rounded-pill bg-light-warning">Draft</span>;
        case FormStatus.ACTIVE:
            return <span className="badge rounded-pill bg-light-success">Active</span>;
        case FormStatus.INACTIVE:
            return <span className="badge rounded-pill bg-light-danger">Inactive</span>;
        default:
            return <span className="badge rounded-pill bg-light-secondary">Inactive</span>;
    }
};

export default function ListForm({
    items,
    paging,
    handleDelete,
    handleActive,
    handleInActive,
    isSuperAdmin,
}: Readonly<IProps>) {
    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center !tw-px-4">#</th>
                        <th className="!tw-px-4">Form Name</th>
                        <th className="text-center !tw-px-4">Version</th>
                        <th className="!tw-px-4">Description</th>
                        <th className="text-center !tw-px-4">Status</th>
                        <th className="!tw-px-4">Updated by</th>
                        <th className="!tw-px-4">Last Updated</th>
                        <th className="!tw-px-4">Used In</th>
                        <th className="tw-w-[120px] !tw-px-4">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: FormQueryItem, index: number) => (
                        <tr key={item.id}>
                            <td className="text-center !tw-px-4">
                                {genTableIndex(index, paging.limit, paging.current_page)}
                            </td>
                            <td className="!tw-px-4">{item.name}</td>
                            <td className="text-center !tw-px-4">{item.version}</td>
                            <td className="!tw-px-4">{item.description}</td>
                            <td className="text-center !tw-px-4">{getStatusBadge(item.status)}</td>
                            <td className="!tw-px-4">
                                {item?.updater ? item?.updater?.full_name : item?.creator?.full_name}
                            </td>
                            <td className="!tw-px-4">
                                {item.updated_at
                                    ? formatDateTime(item.updated_at, FORMAT_DATE.SHOW_DATE_MINUTE)
                                    : formatDateTime(item.created_at, FORMAT_DATE.SHOW_DATE_MINUTE)}
                            </td>

                            <td
                                title={
                                    item?.workflow_forms?.length > 0
                                        ? item?.workflow_forms
                                              ?.map((item) => `${item?.task_name} - ${item?.workflow_definition?.name}`)
                                              .join(', ')
                                        : ''
                                }
                                className="!tw-px-4"
                            >
                                <div className="role-multiline-ellipsis">
                                    {item?.workflow_forms?.length > 0
                                        ? item?.workflow_forms
                                              ?.map((item) => `${item?.task_name} - ${item?.workflow_definition?.name}`)
                                              .join(', ')
                                        : ''}
                                </div>
                            </td>
                            <td className="text-center !tw-px-4">
                                <div className="tw-flex tw-justify-start">
                                    <Link
                                        to={`/form/edit/${item.id}`}
                                        className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                        title="Edit"
                                    >
                                        <Edit size={14} />
                                    </Link>
                                    {isSuperAdmin && (
                                        <>
                                            {item.status !== FormStatus.ACTIVE && (
                                                <button
                                                    className="btn btn-icon btn-sm btn-flat-success waves-effect"
                                                    title="Activate"
                                                    onClick={() => handleActive?.(item)}
                                                >
                                                    <Power size={14} />
                                                </button>
                                            )}
                                            {item.status === FormStatus.ACTIVE && (
                                                <button
                                                    className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                                    title="Deactivate"
                                                    onClick={() => handleInActive?.(item)}
                                                >
                                                    <Power size={14} />
                                                </button>
                                            )}
                                            {item.status === FormStatus.DRAFT && (
                                                <button
                                                    type="button"
                                                    title="Delete"
                                                    className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                                    onClick={() => handleDelete?.(item.id!)}
                                                >
                                                    <Trash2 size={14} />
                                                </button>
                                            )}{' '}
                                        </>
                                    )}
                                </div>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
