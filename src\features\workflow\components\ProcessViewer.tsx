import React, { useEffect, useRef } from 'react';
import BpmnViewer from 'bpmn-js/lib/NavigatedViewer';
import './ProcessViewer.css'; // chứa class highlight, completed

interface Props {
    xml: string;
    activeTaskIds?: string[];
    completedTaskIds?: string[];
}

const ProcessViewer: React.FC<Props> = ({ xml, activeTaskIds = [], completedTaskIds = [] }) => {
    const viewerRef = useRef<HTMLDivElement>(null);
    const bpmnViewer = useRef<BpmnViewer | null>(null);

    useEffect(() => {
        if (!xml) return;

        const viewer = new BpmnViewer({
            container: viewerRef.current!,
        });

        bpmnViewer.current = viewer;

        viewer.importXML(xml, (err: any) => {
            if (err) {
                return;
            }

            const canvas = viewer.get('canvas');

            // Highlight task đang chạy
            activeTaskIds.forEach((id) => canvas.addMarker(id, 'highlight'));

            // Highlight task đã hoàn thành
            completedTaskIds.forEach((id) => canvas.addMarker(id, 'completed'));

            canvas.zoom('fit-viewport');
        });

        return () => {
            viewer.destroy();
        };
    }, [xml, activeTaskIds, completedTaskIds]);

    return <div ref={viewerRef} style={{ height: '600px', width: '100%' }} />;
};

export default ProcessViewer;
