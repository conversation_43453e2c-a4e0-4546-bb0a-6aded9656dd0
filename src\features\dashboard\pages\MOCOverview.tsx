/* eslint-disable react-hooks/exhaustive-deps */
import { Helmet } from 'react-helmet-async';
import { Filter<PERSON><PERSON>, FilterField } from 'features/dashboard/components/shared';
import MOCStatusSummary from 'features/dashboard/components/moc_overview/MOCStatusSummary';
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from 'features/dashboard/components/moc_overview/MOCAreaChart';
import MOC<PERSON>ype<PERSON><PERSON> from 'features/dashboard/components/moc_overview/MOCTypeChart';
import { useState, useMemo } from 'react';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { AREAS_LIST } from 'services/AreaService';
import { QUERY_KEY } from 'constants/common';
import { keepPreviousData } from '@tanstack/react-query';
import { AreaQueryRes, AreaType } from 'types/OperationalArea';
import useQueryParams from '../../../hooks/useQueryParams';
import {
    OverviewAreaSummaryQuery,
    overviewFilterConfig,
    OverviewSummaryQuery,
    OverviewTypeSummaryQuery,
    SearchOverviewDashboardParam,
} from '../../../types/Dashboard';
import { SearchAuditLogParam } from '../../../types/Logs';
import omitBy from 'lodash/omitBy';
import { convertDateRangeToQueryParams } from '../../../utils/date';
import isUndefined from 'lodash/isUndefined';
import { generateFilters } from '../../../utils/common';
import {
    DASHBOARD_MOC_AREA_SUMMARY,
    DASHBOARD_MOC_STATUS_SUMMARY,
    DASHBOARD_MOC_TYPE_SUMMARY,
} from '../../../services/DashboardService';
import Spinner from '../../../components/partials/Spinner';

export default function MOCOverview() {
    const [isLoading, setIsLoading] = useState(false);

    // Handle filter changes from FilterBar
    const handleFilterChange = (filters: Record<string, string>) => {
        // TODO: Call API here with the filter parameters
        console.log('MOCOverview filters:', filters);
    };

    const { data: areaListData } = useGraphQLQuery<AreaQueryRes>(
        [QUERY_KEY.AREAS],
        AREAS_LIST,
        {
            sort: 'type:ASC',
            filters: [`type:=(${AreaType.SUB_AREA})`],
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );
    const areaOptions = useMemo(
        () =>
            areaListData?.areas_list.map((area) => {
                const label = area.code ? `${area.name} (${area.code})` : area.name;
                return {
                    label,
                    value: area.id,
                };
            }) || [],
        [areaListData]
    );
    const areaList = useMemo(() => areaListData?.areas_list || [], [areaListData]);

    // Define filter fields for MOC Overview
    const filterFields: FilterField[] = [
        {
            key: 'area_id',
            type: 'select',
            placeholder: 'Area',
            options: areaOptions,
            isMulti: true,
        },
        {
            key: 'tracking_status',
            type: 'select',
            placeholder: 'Status',
            staticOptions: 'status',
            isMulti: true,
        },
        {
            key: 'created_at__range',
            type: 'date_range',
            placeholder: 'dd/mm/yyyy - dd/mm/yyyy',
        },
        {
            key: 'type',
            type: 'select',
            placeholder: 'Type',
            staticOptions: 'type',
        },
    ];

    const { queryParams, setQueryParams } = useQueryParams<SearchOverviewDashboardParam>();
    const paramConfig: SearchAuditLogParam = omitBy(
        {
            created_at_from:
                convertDateRangeToQueryParams(queryParams.created_at__range || '')?.created_at_from ?? undefined,
            created_at_to:
                convertDateRangeToQueryParams(queryParams.created_at__range || '')?.created_at_to ?? undefined,
            area_id: queryParams.area_id,
            type: queryParams.type,
            tracking_status: queryParams.tracking_status,
        },
        isUndefined
    );
    const filters = generateFilters(paramConfig, overviewFilterConfig);
    const {
        data: mocStatusSummaryData,
        isLoading: isLoadingMocStatusSummary,
        isFetching: isFetchingMocStatusSummary,
    } = useGraphQLQuery<OverviewSummaryQuery>(
        [QUERY_KEY.OVERVIEW_SUMMARY, filters],
        DASHBOARD_MOC_STATUS_SUMMARY,
        {
            filters: filters.length > 0 ? filters : undefined,
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );
    const mocStatusSummary = useMemo(() => {
        if (!mocStatusSummaryData)
            return {
                totalMOC: 0,
                inProgress: 0,
                complete: 0,
                cancel: 0,
                overdue: 0,
                backlog: 0,
                onTracking: 0,
            };
        const {
            on_tracking: onTracking,
            completed,
            cancelled,
            overdue,
            backlog,
        } = mocStatusSummaryData.dashboard_moc_status_summary;
        return {
            totalMOC: onTracking + completed + cancelled + overdue + backlog,
            inProgress: onTracking + overdue + backlog,
            complete: completed,
            cancel: cancelled,
            overdue,
            backlog,
            onTracking,
        };
    }, [mocStatusSummaryData]);

    const {
        data: mocAreaChartData,
        isLoading: isLoadingMocAreaChart,
        isFetching: isFetchingMocAreaChart,
    } = useGraphQLQuery<OverviewAreaSummaryQuery>(
        [QUERY_KEY.OVERVIEW_AREA_SUMMARY, filters],
        DASHBOARD_MOC_AREA_SUMMARY,
        {
            filters: filters.length > 0 ? filters : undefined,
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );
    const mocAreaSummaryData = useMemo(() => mocAreaChartData?.dashboard_moc_area_summary ?? [], [mocAreaChartData]);

    const {
        data: mocTypeChartData,
        isLoading: isLoadingMocTypeChart,
        isFetching: isFetchingMocTypeChart,
    } = useGraphQLQuery<OverviewTypeSummaryQuery>(
        [QUERY_KEY.OVERVIEW_TYPE_SUMMARY, filters],
        DASHBOARD_MOC_TYPE_SUMMARY,
        {
            filters: filters.length > 0 ? filters : undefined,
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );
    const mocTypeData = useMemo(() => mocTypeChartData?.dashboard_moc_type_summary ?? [], [mocTypeChartData]);

    return (
        <div>
            <Helmet>
                <title>MOC Overview</title>
            </Helmet>
            <div className="content-header">
                <div className="row">
                    <div className="col-md-2 col-12 tw-text-3xl tw-flex tw-items-end">MOC Overview</div>
                    <div className="col-md-10 col-12">
                        <FilterBar fields={filterFields} isLoading={isLoading} />
                    </div>
                </div>
            </div>

            <div className="content-body mt-3">
                {(isLoadingMocStatusSummary ||
                    isLoadingMocAreaChart ||
                    isLoadingMocTypeChart ||
                    isFetchingMocStatusSummary ||
                    isFetchingMocAreaChart ||
                    isFetchingMocTypeChart) && <Spinner />}
                {!isLoadingMocStatusSummary && <MOCStatusSummary mocStatusSummary={mocStatusSummary} />}
                {!isLoadingMocAreaChart && <MOCAreaChart mocAreaSummaryData={mocAreaSummaryData} />}
                {!isLoadingMocTypeChart && <MOCTypeChart mocTypeData={mocTypeData} />}
            </div>
        </div>
    );
}
