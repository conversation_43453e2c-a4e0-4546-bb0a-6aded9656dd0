import { FormProvider, useForm } from 'react-hook-form';
import SelectGateKeepersReview from './SelectGateKeepersReview';
import { UserTaskEntity } from 'types/Workflow';
import classNames from 'classnames';

interface Props {
    formDataApprove: UserTaskEntity[];
    dataPlanAndAreaUnit?: any;
    name: string;
}

export default function SelectGateKeepersReviewWrapper({ formDataApprove, dataPlanAndAreaUnit, name }: Props) {
    return (
        <div>
            <label className={'form-label tw-text-[14px] tw-font-bold'}>
                Result Selection Of Gate Keepers (Read-Only)
            </label>
            {formDataApprove?.map((formTask, index) => {
                const selectedGatekeeper = formTask?.form_data?.selected_gatekeeper;

                const formFields = formTask?.form?.schema?.form_fields || [];

                const getKeyRemark = formFields.find((item) => item?.isRemark)?.key;

                const remarkValue = getKeyRemark ? formTask?.form_data?.[getKeyRemark] : '';

                if (!selectedGatekeeper || Object.keys(selectedGatekeeper).length === 0) {
                    return null;
                }

                const methods = useForm({
                    shouldUnregister: true,
                    defaultValues: {
                        selected_gatekeeper: selectedGatekeeper,
                    },
                });

                return (
                    <div key={index} className=" tw-bg-[whitesmoke]">
                        <FormProvider {...methods}>
                            <div className="tw-mb-6 tw-p-3">
                                <SelectGateKeepersReview
                                    name={name}
                                    control={methods.control}
                                    formDataApprove={[formTask]}
                                    dataPlanAndAreaUnit={dataPlanAndAreaUnit}
                                    selectName={formTask.assigneeInfo.full_name}
                                    selectRoles={formTask.userRoles}
                                />
                                <div
                                    key={index}
                                    style={{
                                        width: `100%`,
                                        paddingInline: '6px',
                                        paddingBlock: '10px',
                                    }}
                                >
                                    <label className={classNames('form-label tw-text-[14px] tw-font-bold')}>
                                        Remark
                                    </label>

                                    <textarea
                                        className={'form-control'}
                                        style={{
                                            height: `80px`,
                                            maxHeight: '180px',
                                            background: 'transparent',
                                        }}
                                        value={remarkValue}
                                        disabled
                                    />
                                </div>
                            </div>
                        </FormProvider>
                    </div>
                );
            })}
        </div>
    );
}
