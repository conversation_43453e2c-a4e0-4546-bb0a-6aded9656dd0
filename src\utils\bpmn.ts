export function CustomPaletteProvider(palette: any, create: any, elementFactory: any, translate: any) {
    palette.registerProvider({
        getPaletteEntries: function (element: any): any {
            function createUserTask(event: any) {
                const shape = elementFactory.createShape({
                    type: 'bpmn:UserTask',
                });

                create.start(event, shape);
            }

            return {
                'create.user-task': {
                    group: 'activity',
                    className: 'bpmn-icon-user-task',
                    title: translate('Create User Task'),
                    action: {
                        dragstart: createUserTask,
                        click: createUserTask,
                    },
                },
            };
        },
    });
}

export function findFirstUserTaskElementWithFormKeyOrEmptyUsingRegistry(
    startId: string,
    elementRegistry: any
): any | null {
    const visited = new Set<string>();

    function traverse(elementId: string): any | null {
        if (!elementId || visited.has(elementId)) return null;
        visited.add(elementId);

        const element = elementRegistry.get(elementId);
        if (!element) return null;

        const bo = element.businessObject;

        if (bo.$type === 'bpmn:UserTask') return element;

        if (bo.$type === 'bpmn:SubProcess' && Array.isArray(bo.flowElements)) {
            for (const subElement of bo.flowElements) {
                const result = traverse(subElement.id);
                if (result) return result;
            }
        }

        if (Array.isArray(bo.outgoing)) {
            for (const flow of bo.outgoing) {
                const targetId = flow.targetRef?.id;
                const result = traverse(targetId);
                if (result) return result;
            }
        }

        return null;
    }

    return traverse(startId);
}

export function extractTopLevelSubProcessesInOrder(xml: string) {
    const parser = new DOMParser();
    const doc = parser.parseFromString(xml, 'text/xml');

    const BPMN_NS = 'http://www.omg.org/spec/BPMN/20100524/MODEL';
    const CAMUNDA_NS = 'http://camunda.org/schema/1.0/bpmn';

    const sequenceFlows = Array.from(doc.getElementsByTagNameNS(BPMN_NS, 'sequenceFlow'));
    const elementsById: Record<string, Element> = {};

    // Lưu toàn bộ element vào map
    const allElements = Array.from(doc.getElementsByTagNameNS(BPMN_NS, '*'));
    allElements.forEach((el) => {
        const id = el.getAttribute('id');
        if (id) elementsById[id] = el;
    });

    // Tìm start event
    const startEvent = doc.getElementsByTagNameNS(BPMN_NS, 'startEvent')[0];
    if (!startEvent) return [];

    const visited = new Set<string>();
    const result: {
        index: number | string;
        id: string;
        name: string;
        properties: { name: string; value: string }[];
    }[] = [];

    function isTopLevel(el: Element): boolean {
        let parent = el.parentElement;
        while (parent) {
            if (parent.localName === 'subProcess') return false;
            parent = parent.parentElement;
        }
        return true;
    }

    function extractCamundaProperties(subProcessEl: Element): { name: string; value: string }[] {
        const properties: { name: string; value: string }[] = [];

        const extensionElements = Array.from(subProcessEl.getElementsByTagNameNS(BPMN_NS, 'extensionElements'));
        for (const extEl of extensionElements) {
            const camundaProps = Array.from(extEl.getElementsByTagNameNS(CAMUNDA_NS, 'properties'));
            for (const props of camundaProps) {
                const propList = Array.from(props.getElementsByTagNameNS(CAMUNDA_NS, 'property'));
                for (const prop of propList) {
                    const name = prop.getAttribute('name') || '';
                    const value = prop.getAttribute('value') || '';
                    properties.push({ name, value });
                }
            }
        }

        return properties;
    }

    function traverse(currentId: string) {
        if (visited.has(currentId)) return;
        visited.add(currentId);

        const currentEl = elementsById[currentId];
        if (!currentEl) return;

        if (currentEl.localName === 'subProcess' && isTopLevel(currentEl)) {
            result.push({
                index: 0,
                id: currentId,
                name: currentEl.getAttribute('name') || '',
                properties: extractCamundaProperties(currentEl),
            });
        }

        const nextFlows = sequenceFlows.filter((flow) => flow.getAttribute('sourceRef') === currentId);
        for (const flow of nextFlows) {
            const targetId = flow.getAttribute('targetRef');
            if (targetId) traverse(targetId);
        }
    }

    traverse(startEvent.getAttribute('id')!);

    return result.map((sp, idx) => ({
        ...sp,
        index: String(idx + 1),
    }));
}
