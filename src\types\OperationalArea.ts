import { BaseModelString } from './common';

export enum AreaType {
    ZONE = 1,
    MAIN_AREA = 2,
    SUB_AREA = 3,
    FUNCTIONAL_AREA = 4,
}

export const AreaTypeNames = [
    { id: AreaType.ZONE, name: 'Zone' },
    { id: AreaType.MAIN_AREA, name: 'Main Area' },
    { id: AreaType.SUB_AREA, name: 'Sub Area' },
    { id: AreaType.FUNCTIONAL_AREA, name: 'Functional Area' },
];

export interface User {
    id: string;
    email: string;
    full_name: string;
}

export interface Role {
    id: string;
    name: string;
    users: User[];
}

export default interface Area extends BaseModelString {
    type: AreaType;
    name: string;
    code?: string;
    parent_area_id?: string | null;
    parentArea?: Area;
    children?: Area[];
    roles?: Role[];
    out_of_service?: boolean;
}

export interface AreaQueryRes {
    areas_list: Area[];
}

export interface AreasListBySubArea {
    areas_list_by_sub_area: Area[];
}

export interface AreaCreateRes {
    area_create: Area;
}

export interface AreaUpdateRes {
    area_update: Area;
}

export interface AllAreaQueryRes {
    areas_list_all: Area[];
}
