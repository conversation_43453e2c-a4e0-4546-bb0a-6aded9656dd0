import ContentHeader from 'components/partials/ContentHeader';
import PaginationTable from 'components/partials/PaginationTable';
import Spinner from 'components/partials/Spinner';
import { COMMON_MESSAGE, FILTER_CONDITIONS, PAGINATION, QUERY_KEY } from 'constants/common';
import useQueryParams from 'hooks/useQueryParams';
import isUndefined from 'lodash/isUndefined';
import omitBy from 'lodash/omitBy';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { FormDashboardQuery, FormQuery, FormQueryItem } from '../../../types/Form';
import { DELETE_FORM, FORM_ACTIVE, FORM_DASHBOARD, FORM_INACTIVE, FORM_LIST } from 'services/FormService';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { keepPreviousData } from '@tanstack/react-query';
import { SearchTaskParam } from 'types/Task';
import ListForm from '../components/ListForm';
import FormStatistics from '../components/FormStatistics';
import SearchFormParams from '../components/SearchFormParams';
import { generateFilters, showToast } from 'utils/common';
import { useMemo, useState } from 'react';
import ModalConfirm from 'components/partials/ModalConfirm';
import { useGraphQLMutation } from 'hooks/useGraphQLMutation';
import { useAppStore } from 'stores/appStore';
import { useAuthStore } from '../../../stores/authStore';
import { AuthGroups } from '../../../types/User';

const formFilterConfig = {
    status: { key: 'status', operator: FILTER_CONDITIONS.IN },
};

export default function FormList() {
    const { t } = useTranslation();
    const [showDelete, setShowDelete] = useState(false);
    const [showActive, setShowActive] = useState(false);
    const [showInActive, setShowInActive] = useState(false);
    const [formActive, setFormActive] = useState<FormQueryItem>();
    const { queryParams, setQueryParams } = useQueryParams<SearchTaskParam>();
    const paramConfig: SearchTaskParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search: queryParams.search,
            status: queryParams.status,
        },
        isUndefined
    );

    const setIsLoadingApp = useAppStore((state) => state.setIsLoadingApp);
    const isLoadingApp = useAppStore((state) => state.isLoadingApp);
    const currentUser = useAuthStore((state) => state.user);

    const isSuperAdmin = useMemo(() => currentUser?.auth_group === AuthGroups.SUPER_ADMIN, [currentUser?.auth_group]);
    const isAdmin = useMemo(() => currentUser?.auth_group === AuthGroups.ADMIN, [currentUser?.auth_group]);

    const { search, limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, formFilterConfig);

    const { data, isLoading, isRefetching, refetch } = useGraphQLQuery<FormQuery>(
        [QUERY_KEY.FORM_LIST, queryParams],
        FORM_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            search: search || undefined,
            sort: undefined,
            filters: filters.length > 0 ? filters : undefined,
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    const { data: formDashboard, refetch: refetchFormDashboard } = useGraphQLQuery<FormDashboardQuery>(
        [QUERY_KEY.FORM_DASHBOARD],
        FORM_DASHBOARD,
        {},
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    const activeFormMutation = useGraphQLMutation<{ form_inactivate: FormQueryItem }, { id: string }>(FORM_ACTIVE, '', {
        onSuccess: () => {
            showToast(true, ['Form active successfully']);
            refetch();
            refetchFormDashboard();
            setShowActive(false);
        },
        onSettled: () => {
            setIsLoadingApp(false);
        },
    });

    const inActiveFormMutation = useGraphQLMutation<{ form_inactivate: FormQueryItem }, { id: string }>(
        FORM_INACTIVE,
        '',
        {
            onSuccess: () => {
                showToast(true, ['Form inactive successfully']);
                refetch();
                refetchFormDashboard();
                setShowInActive(false);
            },
            onSettled: () => {
                setIsLoadingApp(false);
            },
        }
    );

    const forms = data?.form_list?.data || [];
    const totalCount = data?.form_list?.totalCount || 0;

    const paging = {
        count_item: totalCount,
        total_page: Math.ceil(totalCount / Number(limit)),
        current_page: Number(page),
        limit: Number(limit),
    };

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({
            ...paramConfig,
            page: page.toString(),
        });
    };

    const [deleteId, setDeleteId] = useState('');
    const handleDelete = (id: string) => {
        setDeleteId(id);
        setShowDelete(true);
    };
    const handleActive = (item: FormQueryItem) => {
        setFormActive(item);
        setShowActive(true);
    };

    const activeItem = () => {
        setShowActive(false);
        setIsLoadingApp(true);
        activeFormMutation.mutate({ id: formActive!.id });
    };
    const inActiveItem = () => {
        setShowInActive(false);
        setIsLoadingApp(true);
        inActiveFormMutation.mutate({ id: formActive!.id });
    };

    const handleInActive = (item: FormQueryItem) => {
        setFormActive(item);
        setShowInActive(true);
    };

    const deleteItem = () => {
        if (!deleteId) return;
        setShowDelete(false);
        setIsLoadingApp(true);
        deleteMutation.mutate({ id: deleteId });
    };

    const deleteMutation = useGraphQLMutation(DELETE_FORM, '', {
        onSuccess: () => {
            showToast(true, ['Delete form successfully']);
            setQueryParams({
                ...paramConfig,
                page: '1',
            });
            refetch();
            refetchFormDashboard();
        },
        onSettled: () => {
            setIsLoadingApp(false);
        },
    });

    return (
        <>
            <Helmet>
                <title>{t('Form')}</title>
            </Helmet>
            <ContentHeader
                title={t('Form')}
                contextMenu={
                    isSuperAdmin || isAdmin
                        ? [
                              {
                                  text: `${t('Create New Form')}`,
                                  to: '/form/add',
                                  icon: 'PLUS',
                              },
                          ]
                        : []
                }
            />
            <div className="content-body">
                <div className="col-12">
                    <FormStatistics formDashboard={formDashboard} />

                    <SearchFormParams isLoading={isLoading} />

                    {(isLoading || isLoadingApp || isRefetching) && <Spinner />}

                    {!isLoading && !isLoadingApp && !isRefetching && (
                        <div className="card">
                            <ListForm
                                items={forms}
                                paging={paging}
                                handleDelete={handleDelete}
                                handleActive={handleActive}
                                handleInActive={handleInActive}
                                isSuperAdmin={isSuperAdmin}
                            />
                            <PaginationTable
                                countItem={totalCount}
                                totalPage={paging.total_page}
                                currentPage={paging.current_page}
                                handlePageChange={handlePageChange}
                            />
                        </div>
                    )}
                </div>{' '}
                <ModalConfirm
                    show={showDelete}
                    text={COMMON_MESSAGE.DELETE_CONFIRM}
                    btnDisabled={false}
                    changeShow={(s: boolean) => setShowDelete(s)}
                    submitAction={deleteItem}
                    isDelete={true}
                    isLoading={isLoadingApp}
                    textTitle="Confirm Delete Form"
                />
                <ModalConfirm
                    show={showActive}
                    text={`Are you sure you want to activate form ${formActive?.name}? This will make the form active and available for use in workflows.`}
                    btnDisabled={false}
                    changeShow={(s: boolean) => setShowActive(s)}
                    submitAction={activeItem}
                    textTitle="Confirm Activate Form "
                    isLoading={isLoadingApp}
                />
                <ModalConfirm
                    show={showInActive}
                    text={`Are you sure you want to deactivate form ${formActive?.name}? This will make the form active and available for use in workflows.`}
                    btnDisabled={false}
                    changeShow={(s: boolean) => setShowInActive(s)}
                    submitAction={inActiveItem}
                    textTitle="Confirm Deactivate Form "
                    isLoading={isLoadingApp}
                />
            </div>
        </>
    );
}
