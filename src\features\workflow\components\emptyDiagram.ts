export const emptyDiagram = `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="3.3.5">
  <bpmn:process id="Process_124lhp5" name="process" isExecutable="true">
    <bpmn:startEvent id="Event_0wpckeb">
      <bpmn:outgoing>Flow_1q7dydm</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Activity_1xedt4p">
      <bpmn:incoming>Flow_1q7dydm</bpmn:incoming>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1q7dydm" sourceRef="Event_0wpckeb" targetRef="Activity_1xedt4p" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_124lhp5">
      <bpmndi:BPMNShape id="Event_0wpckeb_di" bpmnElement="Event_0wpckeb">
        <dc:Bounds x="252" y="182" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1g50ohb_di" bpmnElement="Activity_1xedt4p">
        <dc:Bounds x="330" y="160" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1q7dydm_di" bpmnElement="Flow_1q7dydm">
        <di:waypoint x="288" y="200" />
        <di:waypoint x="330" y="200" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>`;
