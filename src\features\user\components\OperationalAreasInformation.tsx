import React from 'react';
import { UserAccount, UserAreas } from '../../../types/User';

interface IProps {
    user: UserAccount;
}

const OperationalAreasInformation = ({ user }: IProps) => {
    const getGroupedAreas = () => {
        if (!user?.userAreas) return [];

        const grouped: { [key: string]: { zone: string; mainArea: string; subAreas: string[] } } = {};

        user.userAreas.forEach((userArea: UserAreas) => {
            const subArea = userArea.area;
            const mainArea = subArea.parentArea;
            const zone = mainArea?.parentArea;

            if (zone && mainArea && subArea) {
                const key = `${zone.id}-${mainArea.id}`;

                if (!grouped[key]) {
                    grouped[key] = {
                        zone: zone.name,
                        mainArea: mainArea.name,
                        subAreas: [],
                    };
                }

                const subAreaDisplay = subArea.code ? `${subArea.name} (${subArea.code})` : subArea.name;
                if (!grouped[key].subAreas.includes(subAreaDisplay)) {
                    grouped[key].subAreas.push(subAreaDisplay);
                }
            }
        });

        return Object.values(grouped);
    };

    return (
        <div className="card">
            <div className="card-header border-bottom">
                <h5 className="card-title mb-0">Operation Area</h5>
            </div>
            <div className="card-body pt-2">
                {getGroupedAreas().length > 0 ? (
                    getGroupedAreas().map((group, index) => (
                        <div key={index} className="mb-3">
                            <div className="d-flex mb-1">
                                <label className="fw-bold me-2" style={{ minWidth: '120px' }}>
                                    Zone:
                                </label>
                                <div>{group.zone}</div>
                            </div>
                            <div className="d-flex mb-1">
                                <label className="fw-bold me-2" style={{ minWidth: '120px' }}>
                                    Main Area:
                                </label>
                                <div>{group.mainArea}</div>
                            </div>
                            <div className="d-flex">
                                <label className="fw-bold me-2" style={{ minWidth: '120px' }}>
                                    Sub Area:
                                </label>
                                <div>{group.subAreas.join(', ')}</div>
                            </div>
                            {index < getGroupedAreas().length - 1 && <hr className="my-3" />}
                        </div>
                    ))
                ) : (
                    <div className="text-muted">No areas assigned</div>
                )}
            </div>
        </div>
    );
};

export default OperationalAreasInformation;
