import classNames from 'classnames';
import React from 'react';
import { TaskStatus } from 'types/Task';
import { UserTaskEntity } from 'types/Workflow';
import { getOrdinalSuffix, getStatusBadgeStep } from 'utils/common';
import { FORMAT_DATE, formatDateTime } from 'utils/date';

interface TableReviewProps {
    data: UserTaskEntity[];
}

export const TableReview = ({ data }: TableReviewProps) => {
    const distinctOrders = [...new Set(data?.map((item) => item.order))];
    const showOrderSeparator = distinctOrders.length > 1;
    return (
        <div className={classNames('card tw-p-8 tw-max-w-[1000px] tw-mx-auto border tw-mb-8  tw-bg-[whitesmoke]')}>
            <p className="tw-font-bold tw-mb-2 tw-max-w-[1000px] ">Review Result</p>
            <div className="tw-overflow-auto tw-max-w-[1000px] tw-bg-white">
                <table className="tw-w-full tw-border tw-border-collapse">
                    <thead className="tw-bg-gray-100">
                        <tr>
                            <th className="tw-border tw-p-2 tw-text-center tw-w-[5%]">No</th>
                            <th className="tw-border tw-p-2 tw-w-[15%]">Approval Role</th>
                            <th className="tw-border tw-p-2 tw-w-[15%]">Zone Name</th>
                            <th className="tw-border tw-p-2 tw-w-[15%]">Reviewer Name</th>
                            <th className="tw-border tw-p-2 tw-w-[15%]">Updated On</th>
                            <th className="tw-border tw-p-2 tw-text-center tw-w-[10%]">Decision</th>
                            <th className="tw-border tw-p-2 tw-w-[25%]">Remark</th>
                        </tr>
                    </thead>
                    <tbody>
                        {data.map((item, index) => {
                            const getKeyRemark = item?.form?.schema?.form_fields?.find((item) => item?.isRemark)?.key;

                            const isLastOfSameOrder =
                                index === data.length - 1 || item?.order !== data[index + 1]?.order;

                            return (
                                <React.Fragment key={index}>
                                    <tr>
                                        <td className="tw-border tw-p-2 tw-text-center">{index + 1}</td>
                                        <td className="tw-border tw-p-2">
                                            {item?.userRoles?.map((item) => item.name).join(', ')}
                                        </td>
                                        <td className="tw-border tw-p-2">
                                            {item?.user_zones?.map((item) => item.name).join(', ')}
                                        </td>
                                        <td className="tw-border tw-p-2">{item?.assigneeInfo?.full_name}</td>
                                        <td className="tw-border tw-p-2">
                                            {item?.status !== TaskStatus.IN_PROGRESS &&
                                                formatDateTime(item?.updated_at, FORMAT_DATE.SHOW_DATE_MINUTE)}
                                        </td>
                                        <td className="tw-border tw-p-2 tw-text-center">
                                            {getStatusBadgeStep(item?.status)}
                                        </td>
                                        <td className="tw-border tw-p-2">
                                            {getKeyRemark && item?.form_data?.[getKeyRemark]}
                                        </td>
                                    </tr>

                                    {showOrderSeparator && isLastOfSameOrder && (
                                        <tr>
                                            <td colSpan={7}>
                                                <div className="tw-text-lg tw-font-bold tw-my-4 tw-text-center tw-flex tw-items-center tw-gap-2 tw-mx-auto">
                                                    <div className="tw-flex-1 tw-border-t tw-border-gray-300"></div>
                                                    <span className="tw-px-2">{getOrdinalSuffix(item.order)}</span>
                                                    <div className="tw-flex-1 tw-border-t tw-border-gray-300"></div>
                                                </div>
                                            </td>
                                        </tr>
                                    )}
                                </React.Fragment>
                            );
                        })}
                    </tbody>
                </table>
            </div>
        </div>
    );
};
