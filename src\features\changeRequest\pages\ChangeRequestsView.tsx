/* eslint-disable react-hooks/exhaustive-deps */
import ContentHeader from 'components/partials/ContentHeader';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate, useParams } from 'react-router-dom';
import React, { useEffect, useState } from 'react';
import classNames from 'classnames';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { UserTaskEntity, WorkflowInstanceDetailQuery } from 'types/Workflow';
import { QUERY_KEY } from 'constants/common';
import { WORKFLOW_INSTANCE_DETAIL } from 'services/WorkflowService';
import { keepPreviousData } from '@tanstack/react-query';
import { FormItem } from 'types/Form';
import { TaskStatus } from 'types/Task';
import { FORMAT_DATE, formatDateTime } from '../../../utils/date';
import { getOrdinalSuffix, getStatusBadgeStep, getStatusBadgeStepCircle, isValidFormApproval } from 'utils/common';
import PreviousFormFields from 'features/form/components/PreviousFormFields';
import CollapsibleForm from 'features/task/components/CollapsibleForm';
import { TableReview } from 'components/partials/TableReview';

export default function ChangeRequestsView() {
    const { t } = useTranslation();
    const { id } = useParams<{ id: string }>();
    const [activeTab, setActiveTab] = useState<string>('moc-details');
    const navigate = useNavigate();

    const { data, isLoading } = useGraphQLQuery<WorkflowInstanceDetailQuery>(
        [QUERY_KEY.WORKFLOWS_INSTANCE_DETAIL, id],
        WORKFLOW_INSTANCE_DETAIL,
        { id },
        '',
        {
            enabled: !!id,
            placeholderData: keepPreviousData,
        }
    );

    useEffect(() => {
        if (!isLoading && !data) {
            navigate('/not-found');
        }
    }, [data, isLoading]);

    const getFormInitial = data?.workflow_instances_detail?.workflow_definition?.workflow_forms?.find(
        (item) => item.is_first_form
    )?.form?.schema as FormItem;
    const getFormDataInitial = data?.workflow_instances_detail?.form_data;

    const isFirstUserTaskName = data?.workflow_instances_detail?.workflow_definition?.workflow_forms?.find(
        (item) => item.is_first_form
    );

    const handleTabClick = (tabId: string) => {
        setActiveTab(tabId);
    };

    const groupedByTaskKey: Record<string, UserTaskEntity[]> = (data?.workflow_instances_detail?.user_tasks || [])
        .filter((task) => task.status !== TaskStatus.IN_PROGRESS)
        .reduce((acc, task) => {
            const key = task.task_key;
            if (!acc[key]) acc[key] = [];
            acc[key].push(task);
            return acc;
        }, {} as Record<string, UserTaskEntity[]>);

    if (isLoading) {
        return (
            <>
                <Helmet>
                    <title>{t('Change Request')}</title>
                </Helmet>
                <ContentHeader title={t('Change Request')} />
                <div className="content-body">
                    <div className="col-12">
                        <div className="d-flex justify-content-center">
                            <div className="spinner-border text-primary" role="status">
                                <span className="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </>
        );
    }

    return (
        <>
            <Helmet>
                <title>{t('Change Request')}</title>
            </Helmet>
            <ContentHeader
                title={
                    <>
                        <Link to="/changeRequest">Change Request</Link>
                    </>
                }
                breadcrumbs={[{ text: data?.workflow_instances_detail?.business_key }]}
            />
            <div className="content-body">
                <div className="col-12">
                    {/* Header section */}
                    <div className="card mb-2">
                        <div className="card-body">
                            <div className="tw-flex tw-flex-col tw-gap-4">
                                <div className="d-flex justify-content-between align-items-center">
                                    <h1 className="mb-0 tw-font-medium">
                                        Request Title: {data?.workflow_instances_detail?.name}
                                    </h1>
                                </div>
                                <div className="tw-grid sm:tw-grid-cols-2 lg:tw-grid-cols-2 xl:tw-grid-cols-4 tw-gap-4 tw-w-full">
                                    <div className="tw-flex tw-items-start tw-gap-2 tw-border-r">
                                        {' '}
                                        <span className="tw-font-medium tw-flex-shrink-0">Current Step: </span>
                                        {data?.workflow_instances_detail?.current_user_task?.workflow_step.step_name}
                                    </div>

                                    <div className="tw-flex tw-items-start tw-gap-2 tw-border-r-0 xl:tw-border-r">
                                        <span className="tw-font-medium tw-flex-shrink-0">Current Status: </span>
                                        {data?.workflow_instances_detail?.current_user_task?.status &&
                                            getStatusBadgeStep(
                                                data?.workflow_instances_detail?.current_user_task?.status
                                            )}
                                        <span className="badge bg-warning"></span>
                                    </div>

                                    <div className="tw-flex tw-items-start tw-gap-2 tw-border-r">
                                        <span className="tw-font-medium tw-flex-shrink-0">Workflow: </span>
                                        {data?.workflow_instances_detail?.workflow_definition?.name}
                                    </div>

                                    <div className="tw-flex tw-items-start tw-gap-2">
                                        <span className="tw-font-medium tw-flex-shrink-0">Version: </span>{' '}
                                        {data?.workflow_instances_detail?.workflow_definition?.version}
                                    </div>
                                </div>
                                <div className="tw-grid sm:tw-grid-cols-2 lg:tw-grid-cols-2 xl:tw-grid-cols-4 tw-gap-4 tw-w-full">
                                    <div className="tw-flex tw-items-start tw-gap-2 tw-border-r">
                                        <span className="tw-font-medium tw-flex-shrink-0">Created By: </span>{' '}
                                        {data?.workflow_instances_detail?.creator?.full_name}
                                    </div>

                                    <div className="tw-flex tw-items-start tw-gap-2 tw-border-r-0 xl:tw-border-r">
                                        <span className="tw-font-medium tw-flex-shrink-0">Created At: </span>{' '}
                                        {formatDateTime(
                                            data?.workflow_instances_detail?.created_at,
                                            FORMAT_DATE.SHOW_DATE_MINUTE
                                        )}
                                    </div>

                                    <div className="tw-flex tw-items-start tw-gap-2 tw-border-r">
                                        <span className="tw-font-medium tw-flex-shrink-0">Last Updated By: </span>{' '}
                                        {data?.workflow_instances_detail?.updater?.full_name ??
                                            data?.workflow_instances_detail?.creator?.full_name}
                                    </div>

                                    <div className="tw-flex tw-items-start tw-gap-2">
                                        <span className="tw-font-medium tw-flex-shrink-0">Last Updated At: </span>{' '}
                                        {formatDateTime(
                                            data?.workflow_instances_detail?.updated_at ??
                                                data?.workflow_instances_detail?.created_at,
                                            FORMAT_DATE.SHOW_DATE_MINUTE
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Tabs */}
                    <div className="card">
                        <div className="card-body">
                            <ul className="nav nav-tabs mb-2" role="tablist">
                                <li className="nav-item">
                                    <a
                                        className={classNames('nav-link cursor-pointer', {
                                            active: activeTab === 'moc-details',
                                        })}
                                        onClick={() => handleTabClick('moc-details')}
                                    >
                                        MOC Details
                                    </a>
                                </li>
                                <li className="nav-item">
                                    <a
                                        className={classNames('nav-link cursor-pointer', {
                                            active: activeTab === 'step-progress',
                                        })}
                                        onClick={() => handleTabClick('step-progress')}
                                    >
                                        Step Progress
                                    </a>
                                </li>
                            </ul>

                            {/* Tab content */}
                            <div className="tab-content">
                                {activeTab === 'moc-details' && (
                                    <div className="tab-pane active">
                                        <CollapsibleForm
                                            title={isFirstUserTaskName?.task_name || ''}
                                            defaultOpen={false}
                                        >
                                            <div className="tw-px-8 !tw-py-[100px] pb-0">
                                                <PreviousFormFields
                                                    initialData={getFormDataInitial}
                                                    status={TaskStatus.SUBMITTED}
                                                    createBy={data?.workflow_instances_detail.creator?.full_name || ''}
                                                    schemaForm={getFormInitial as FormItem}
                                                    dataPlanAndAreaUnit={
                                                        data?.workflow_instances_detail?.form_data
                                                            ?.area_of_implementation
                                                    }
                                                    assignee={data?.workflow_instances_detail?.created_by}
                                                    firstAreaOwner={
                                                        data?.workflow_instances_detail?.camunda_variables
                                                            ?.firstAreaOwner
                                                    }
                                                    stepName={isFirstUserTaskName?.task_name}
                                                />
                                            </div>
                                        </CollapsibleForm>
                                        {Object.entries(groupedByTaskKey).map(([taskKey, tasks]) => {
                                            const taskName = tasks[0]?.task_name || taskKey;

                                            const isFormApproval = isValidFormApproval(tasks);

                                            return (
                                                <CollapsibleForm key={taskKey} title={taskName} defaultOpen={false}>
                                                    <div className="tw-px-8 !tw-py-[100px] pb-0 space-y-4">
                                                        {isFormApproval ? (
                                                            <TableReview data={tasks} />
                                                        ) : (
                                                            tasks.map((task, index) => {
                                                                const isLastOfSameOrder =
                                                                    index === tasks.length - 1 ||
                                                                    task?.order !== tasks[index + 1]?.order;

                                                                const distinctOrders = [
                                                                    ...new Set(tasks?.map((item) => item.order)),
                                                                ];
                                                                const showOrderSeparator = distinctOrders.length > 1;

                                                                return (
                                                                    <React.Fragment key={index}>
                                                                        <PreviousFormFields
                                                                            initialData={task?.form_data}
                                                                            status={task.status}
                                                                            createBy={task.assigneeInfo?.full_name}
                                                                            schemaForm={task?.form?.schema as FormItem}
                                                                            dataPlanAndAreaUnit={
                                                                                task?.workflow_instance?.form_data
                                                                                    ?.area_of_implementation
                                                                            }
                                                                            assignee={task?.assignee}
                                                                            firstAreaOwner={
                                                                                data?.workflow_instances_detail
                                                                                    ?.camunda_variables?.firstAreaOwner
                                                                            }
                                                                            stepName={task?.task_name}
                                                                            isFileEntry
                                                                        />
                                                                        {showOrderSeparator && isLastOfSameOrder && (
                                                                            <div className="tw-text-lg tw-max-w-[1000px] tw-font-bold tw-mb-8 tw-text-center tw-flex tw-items-center tw-gap-2 tw-mx-auto">
                                                                                <div className="tw-flex-1 tw-border-t tw-border-gray-300"></div>
                                                                                <span className="tw-px-2">
                                                                                    {getOrdinalSuffix(task.order)}
                                                                                </span>
                                                                                <div className="tw-flex-1 tw-border-t tw-border-gray-300"></div>
                                                                            </div>
                                                                        )}
                                                                    </React.Fragment>
                                                                );
                                                            })
                                                        )}
                                                    </div>
                                                </CollapsibleForm>
                                            );
                                        })}
                                    </div>
                                )}

                                {activeTab === 'step-progress' && (
                                    <div className="tab-pane active">
                                        <div className="tw-flex tw-justify-center tw-items-center tw-gap-4 tw-mb-6">
                                            {getStatusBadgeStepCircle(TaskStatus.SUBMITTED)}
                                            <div className="card tw-p-6 tw-mb-0 tw-max-w-[1000px] tw-w-full border">
                                                <div className="content-body tw-flex tw-flex-col tw-gap-2">
                                                    <div className="tw-flex tw-gap-2 tw-flex-wrap">
                                                        <p className="tw-font-bold">Step:</p>
                                                        <p>{isFirstUserTaskName?.task_name}</p>
                                                    </div>
                                                    <div className="tw-flex tw-gap-2 tw-flex-wrap">
                                                        <p className="tw-font-bold">Status:</p>{' '}
                                                        {getStatusBadgeStep(TaskStatus.SUBMITTED)}
                                                    </div>
                                                    <div className="tw-flex tw-gap-2 tw-flex-wrap">
                                                        <p className="tw-font-bold">Completed By:</p>
                                                        <p>
                                                            {data?.workflow_instances_detail.creator?.full_name}{' '}
                                                            (Originator){' '}
                                                        </p>
                                                    </div>
                                                    <div className="tw-flex tw-gap-2 tw-flex-wrap">
                                                        <p className="tw-font-bold">Completed Date:</p>
                                                        <p>
                                                            {' '}
                                                            {formatDateTime(
                                                                data?.workflow_instances_detail?.updated_at,
                                                                FORMAT_DATE.SHOW_DATE_MINUTE
                                                            )}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>{' '}
                                        </div>
                                        {data?.workflow_instances_detail?.user_tasks
                                            .filter((task) => task.status !== TaskStatus.DELETED)
                                            .map((task, index) => (
                                                <div
                                                    key={index}
                                                    className="tw-flex tw-justify-center tw-items-center tw-gap-4 tw-mb-6"
                                                >
                                                    {getStatusBadgeStepCircle(task.status)}
                                                    <div className="card tw-mb-0 tw-p-6 tw-max-w-[1000px] tw-w-full border">
                                                        <div className="content-body tw-flex tw-flex-col tw-gap-2">
                                                            <div className="tw-flex tw-gap-2 tw-flex-wrap">
                                                                <p className="tw-font-bold">Step:</p>
                                                                <p>{task?.task_name}</p>
                                                            </div>
                                                            <div className="tw-flex tw-gap-2 tw-flex-wrap">
                                                                <p className="tw-font-bold">Status:</p>{' '}
                                                                {getStatusBadgeStep(task?.status)}
                                                            </div>
                                                            <div className="tw-flex tw-gap-2 tw-flex-wrap">
                                                                {task?.status === TaskStatus.IN_PROGRESS ? (
                                                                    <p className="tw-font-bold">Assigned To:</p>
                                                                ) : (
                                                                    <p className="tw-font-bold">Completed By:</p>
                                                                )}
                                                                <p>
                                                                    {task.assigneeInfo?.full_name}{' '}
                                                                    {task?.userRoles?.length
                                                                        ? `(${task?.userRoles
                                                                              .map((item) => item.name)
                                                                              .join(', ')})`
                                                                        : ''}
                                                                </p>
                                                            </div>
                                                            <div className="tw-flex tw-gap-2 tw-flex-wrap">
                                                                <p className="tw-font-bold">Completed Date:</p>
                                                                <p>
                                                                    {task?.status !== TaskStatus.IN_PROGRESS &&
                                                                        task?.status !== TaskStatus.DELETED &&
                                                                        formatDateTime(
                                                                            task?.updated_at,
                                                                            FORMAT_DATE.SHOW_DATE_MINUTE
                                                                        )}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}
