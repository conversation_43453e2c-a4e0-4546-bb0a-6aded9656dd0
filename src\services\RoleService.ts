import { gql } from 'graphql-request';

export const ROLES_LIST = gql`
    query Roles_list($search: String, $filters: [String!], $sort: String) {
        roles_list(body: { search: $search, filters: $filters, sort: $sort }) {
            id
            type
            name
            is_gatekeeper_child
        }
    }
`;

export const ROLE_DETAIL = gql`
    query Role_detail($id: String!) {
        role_detail(id: $id) {
            id
            is_gatekeeper_child
            type
            name
            userAreaRoles {
                id
                user_id
                area_id
                role_id
                created_at
                user {
                    id
                    full_name
                }
            }
        }
    }
`;

export const CHANGE_USER_ROLE = gql`
    mutation Change_user_role($role_id: String!, $user_id: String!, $area_ids: [String!]) {
        change_user_role(body: { role_id: $role_id, user_id: $user_id, area_ids: $area_ids })
    }
`;

export const REMOVE_USER_ROLE = gql`
    mutation Remove_user_role($id: String!) {
        remove_user_role(id: $id)
    }
`;
