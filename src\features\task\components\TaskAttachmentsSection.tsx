import { Download, FileText, File, Image, Archive, Music, Video } from 'react-feather';
import { FileAttachment } from '../../../types/Task';

interface IProps {
    fileAttachments: FileAttachment[];
}

export default function TaskAttachmentsSection({ fileAttachments }: Readonly<IProps>) {
    // Hàm để lấy icon theo loại file
    const getFileIcon = (fileName: string) => {
        const extension = fileName.split('.').pop()?.toLowerCase();

        switch (extension) {
            case 'pdf':
                return <FileText size={24} className="tw-text-red-500" />;
            case 'doc':
            case 'docx':
                return <FileText size={24} className="tw-text-blue-500" />;
            case 'xls':
            case 'xlsx':
                return <FileText size={24} className="tw-text-green-500" />;
            case 'ppt':
            case 'pptx':
                return <FileText size={24} className="tw-text-orange-500" />;
            case 'jpg':
            case 'jpeg':
            case 'png':
            case 'gif':
            case 'bmp':
            case 'svg':
                return <Image size={24} className="tw-text-purple-500" />;
            case 'zip':
            case 'rar':
            case '7z':
                return <Archive size={24} className="tw-text-yellow-500" />;
            case 'mp3':
            case 'wav':
            case 'flac':
                return <Music size={24} className="tw-text-pink-500" />;
            case 'mp4':
            case 'avi':
            case 'mov':
            case 'wmv':
                return <Video size={24} className="tw-text-indigo-500" />;
            default:
                return <File size={24} className="tw-text-gray-500" />;
        }
    };

    // Hàm để format file size thành KB
    const formatFileSize = (sizeInBytes: number): string => {
        const sizeInKB = sizeInBytes / 1024;
        return `${sizeInKB.toFixed(1)} KB`;
    };

    // Hàm để handle download file
    const handleDownload = (fileUrl: string, fileName: string) => {
        const link = document.createElement('a');
        link.href = fileUrl;
        link.download = fileName;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    if (!fileAttachments || fileAttachments.length === 0) {
        return <div className="tw-text-center tw-text-gray-400 tw-mt-8">No attachments available.</div>;
    }

    return (
        <div className="tw-space-y-6">
            {fileAttachments.map((attachment) => (
                <div key={attachment.workflow_step_id}>
                    {/* Step Title with divider lines */}
                    <div className="tw-text-lg tw-font-bold tw-text-center tw-flex tw-items-center tw-gap-2 tw-mb-4">
                        <div className="tw-flex-1 tw-border-t tw-border-gray-300"></div>
                        <span className="tw-px-4 tw-text-gray-700">Step: {attachment.workflow_step_name}</span>
                        <div className="tw-flex-1 tw-border-t tw-border-gray-300"></div>
                    </div>

                    {/* Files list */}
                    <div className="tw-space-y-3">
                        {attachment.files.map((file) => (
                            <div
                                key={file.id}
                                className="tw-border tw-border-gray-300 tw-rounded tw-p-4 tw-flex tw-items-center tw-justify-between tw-bg-white hover:tw-bg-gray-50 tw-transition-colors"
                            >
                                <div className="tw-flex tw-items-center tw-gap-3">
                                    {/* File Icon */}
                                    <div className="tw-flex-shrink-0">{getFileIcon(file.file_name)}</div>

                                    {/* File Info */}
                                    <div className="tw-flex-1">
                                        <div className="tw-font-medium tw-text-gray-900 tw-mb-1">{file.file_name}</div>
                                        <div className="tw-text-sm tw-text-gray-500 tw-flex tw-items-center tw-gap-4">
                                            <span>{formatFileSize(file.file_size)}</span>
                                            <span>
                                                Uploaded by: {('creator' in file && file.creator?.full_name) || ''}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                {/* Download Button */}
                                <button
                                    className="tw-flex-shrink-0 tw-p-2 tw-text-gray-600 hover:tw-text-gray-900 tw-bg-transparent tw-border-none tw-cursor-pointer tw-transition-colors"
                                    onClick={() => handleDownload(file.file_url, file.file_name)}
                                    title="Download file"
                                >
                                    <Download size={20} />
                                </button>
                            </div>
                        ))}
                    </div>
                </div>
            ))}
        </div>
    );
}
