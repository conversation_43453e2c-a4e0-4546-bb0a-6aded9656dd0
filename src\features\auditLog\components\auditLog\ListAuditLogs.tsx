import { Eye } from 'react-feather';
import { AuditLog, AuditLogActionTypeNames, AuditLogActionType } from '../../../../types/Logs';
import { FORMAT_DATE, formatDateTime } from '../../../../utils/date';

interface IProps {
    onViewDetail: (logId: string) => void;
    auditLogs: AuditLog[];
}

export default function ListAuditLogs({ onViewDetail, auditLogs }: Readonly<IProps>) {
    const handleViewDetail = (logId: string) => {
        onViewDetail(logId);
    };

    return (
        <div className="table-responsive">
            <table className="table table-sm align-middle mb-0">
                <thead>
                    <tr>
                        <th>Log ID</th>
                        <th>Timestamp</th>
                        <th>Empl. Code</th>
                        <th>User Name</th>
                        <th>Role</th>
                        <th>Action Type</th>
                        <th>MOC Request Title</th>
                        <th>Step Name</th>
                        <th>IP Address</th>
                        <th>Details</th>
                        <th className="text-center">Action</th>
                    </tr>
                </thead>
                <tbody>
                    {auditLogs.map((log) => {
                        let logRoles = '';
                        if (
                            [
                                AuditLogActionType.MOC_CREATE_DRAFT,
                                AuditLogActionType.MOC_SUBMIT_NEW,
                                AuditLogActionType.MOC_UPDATE_DRAFT,
                                AuditLogActionType.MOC_DELETE_DRAFT,
                            ].includes(log.action_type as AuditLogActionType)
                        ) {
                            logRoles = 'Originator';
                        }

                        if (
                            [
                                AuditLogActionType.MOC_APPROVE,
                                AuditLogActionType.MOC_REJECT,
                                AuditLogActionType.MOC_TERMINATE,
                            ].includes(log.action_type as AuditLogActionType)
                        ) {
                            logRoles = log.userTask?.userRoles.map((role) => role.name).join(', ') ?? '';
                        }

                        return (
                            <tr key={log.id}>
                                <td>{log.id}</td>
                                <td>{formatDateTime(log.created_at, FORMAT_DATE.SHOW_DATE_MINUTE)}</td>
                                <td>{log.creator?.oms_emp_code}</td>
                                <td>{log.creator?.full_name}</td>
                                <td>{logRoles}</td>
                                <td>{AuditLogActionTypeNames.find((item) => item.id === log.action_type)?.name}</td>
                                <td>{log.mocRequest?.name}</td>
                                <td>{log.workflow_step?.step_name}</td>
                                <td>{log.ip_address}</td>
                                <td>{log.description}</td>
                                <td className="text-center">
                                    <button
                                        type="button"
                                        className="btn btn-sm btn-icon btn-flat-info waves-effect"
                                        title="View Details"
                                        onClick={() => handleViewDetail(log.id!)}
                                    >
                                        <Eye size={14} />
                                    </button>
                                </td>
                            </tr>
                        );
                    })}
                </tbody>
            </table>
        </div>
    );
}
