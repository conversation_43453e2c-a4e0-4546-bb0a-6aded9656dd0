import { useNavigate } from 'react-router-dom';
import { AuditLogDashboard } from '../../../../types/Logs';

interface IProps {
    auditLogDashboard?: AuditLogDashboard;
}

export default function AuditLogStatistical({ auditLogDashboard }: Readonly<IProps>) {
    const navigate = useNavigate();

    return (
        <div className="tw-grid tw-grid-cols-1 sm:tw-grid-cols-2 tw-gap-4 tw-w-full">
            <button type="button" className="card tw-w-full tw-text-left" onClick={() => navigate('/audit-log')}>
                <div className="card-header">
                    <div>
                        <h2 className="fw-bolder mb-0">{auditLogDashboard?.total}</h2>
                        <p className="card-text">Total Audit Log</p>
                    </div>
                    <div className="avatar bg-light-info p-50 m-0">
                        <div className="avatar-content">
                            <i className="font-medium-5 bi bi-list-task"></i>
                        </div>
                    </div>
                </div>
            </button>
        </div>
    );
}
