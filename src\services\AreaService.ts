import { gql } from 'graphql-request';

function getAreaTree(depth = 4): string {
    const baseFields = `
        id
        type
        name
        code
        parent_area_id
        out_of_service
    `;

    function buildNestedStructure(currentDepth: number): string {
        if (currentDepth <= 0) return baseFields;

        return `
            ${baseFields}
            children {
                ${buildNestedStructure(currentDepth - 1)}
            }
        `;
    }

    return buildNestedStructure(depth);
}

export const AREAS_LIST = gql`
    query Areas_list($search: String, $filters: [String!], $sort: String) {
        areas_list(body: { search: $search, filters: $filters, sort: $sort }) {
            ${getAreaTree()}
        }
    }
`;

export const AREA_CREATE = gql`
    mutation Area_create($body: AreaSaveInput!) {
        area_create(body: $body) {
            id
            created_by
            updated_by
            created_at
            updated_at
            deleted_at
            type
            name
            code
            parent_area_id
        }
    }
`;

export const AREA_UPDATE = gql`
    mutation Area_update($id: String!, $body: AreaSaveInput!) {
        area_update(id: $id, body: $body) {
            id
        }
    }
`;

export const AREAS_LIST_BY_SUB_AREA = gql`
    query AreasListBySubArea($body: AreaFilterUserBySubAreaInputDto!) {
        areas_list_by_sub_area(body: $body) {
            id
            name
            code
            type
            parent_area_id
            created_by
            updated_by
            created_at
            updated_at
            deleted_at
            roles {
                id
                name
                users {
                    id
                    email
                    full_name
                }
            }
        }
    }
`;

export const AREA_OUT_OF_SERVICE = gql`
    mutation Area_out_of_service($id: String!) {
        area_out_of_service(id: $id) {
            id
        }
    }
`;

export const AREAS_LIST_ALL = gql`
    query Areas_list_all($search: String, $filters: [String!], $sort: String) {
        areas_list_all(body: { filters: $filters, sort: $sort, search: $search }) {
            ${getAreaTree()}
        }
    }
`;
