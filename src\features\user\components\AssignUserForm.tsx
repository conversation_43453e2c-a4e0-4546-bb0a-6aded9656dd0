import { useEffect, useState } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { keepPreviousData } from '@tanstack/react-query';
import Select from 'react-select';
import classNames from 'classnames';
import { QUERY_KEY, PAGINATION } from '../../../constants/common';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { USER_LIST_PAGINATE } from '../../../services/UserService';
import { AREAS_LIST } from '../../../services/AreaService';
import { ROLES_LIST } from '../../../services/RoleService';
import {
    UserAccount,
    UserAccountListQuery,
    AuthGroupNames,
    SearchUseAccountrParam,
    AuthGroups,
    userFilterConfig,
} from '../../../types/User';
import { AreaType, AreaQueryRes } from '../../../types/OperationalArea';
import Role, { RoleQueryRes, RoleType } from '../../../types/Role';
import { generateFilters } from '../../../utils/common';
import PaginationTable from '../../../components/partials/PaginationTable';
import Spinner from '../../../components/partials/Spinner';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { SelectOptionString } from '../../../types/common/Item';

interface IProps {
    onUserSelect: (userId: string) => void;
    onClose: () => void;
    onReset?: () => void;
    role?: Role;
    auth_group?: AuthGroups;
    btnContent?: string;
}

interface SearchFormData {
    search: string;
    'userAreas.area_id': string[];
    userAreas: {
        area_id: string[];
    };
    auth_group: number;
    'userAreaRoles.role_id': string[];
    userAreaRoles: {
        role_id: string[];
    };
}

export default function AssignUserForm({
    onUserSelect,
    onClose,
    onReset,
    role,
    auth_group,
    btnContent = 'Assign User',
}: Readonly<IProps>) {
    const [selectedUserId, setSelectedUserId] = useState<string>('');
    const [searchParams, setSearchParams] = useState<SearchUseAccountrParam>({
        limit: PAGINATION.limit.toString(),
        page: '1',
        search: '',
        'userAreas.area_id': '',
        auth_group: auth_group?.toString() || '',
        'userAreaRoles.role_id': '',
        is_active: '1',
    });

    useEffect(() => {
        if (role) {
            setSearchParams((prevParams) => ({
                ...prevParams,
                id: role.type === RoleType.AREA_BASED ? '' : role.userAreaRoles.map((uar) => uar.user_id).join(','),
            }));
        }
    }, [role]);

    const schema = yup
        .object({
            search: yup.string().trim(),
            'userAreas.area_id': yup.array().of(yup.string()),
            auth_group: yup.number(),
            'userAreaRoles.role_id': yup.array().of(yup.string()),
        })
        .required();

    const { control, handleSubmit, reset } = useForm<SearchFormData>({
        resolver: yupResolver(schema),
        defaultValues: {
            search: '',
            'userAreas.area_id': [],
            auth_group: AuthGroups.MOC_MANAGER,
            'userAreaRoles.role_id': [],
        },
    });

    // Load sub areas
    const { data: subAreaData } = useGraphQLQuery<AreaQueryRes>(
        [QUERY_KEY.AREAS, AreaType.SUB_AREA],
        AREAS_LIST,
        {
            filters: [`type:=(${AreaType.SUB_AREA})`],
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );
    const subAreaList: SelectOptionString[] =
        subAreaData?.areas_list.map((area) => ({
            label: area.name,
            value: area.id,
        })) || [];

    // Load roles
    const { data: roleData } = useGraphQLQuery<RoleQueryRes>([QUERY_KEY.ROLES], ROLES_LIST, {}, '', {
        placeholderData: keepPreviousData,
    });
    const roleList: SelectOptionString[] =
        roleData?.roles_list.map((role) => ({
            label: role.name,
            value: role.id,
        })) || [];

    const { search, limit, page, ...dataParamConfig } = searchParams;
    const filters = generateFilters(dataParamConfig, userFilterConfig);

    // Load users
    const { data, isLoading, isRefetching } = useGraphQLQuery<UserAccountListQuery>(
        [QUERY_KEY.USER_ACCOUNT_LIST, searchParams, filters],
        USER_LIST_PAGINATE,
        {
            page: Number(page),
            limit: Number(limit),
            search,
            filters: filters.length > 0 ? filters : undefined,
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    const handlePageChange = (_event: React.ChangeEvent<unknown>, newPage: number) => {
        setSearchParams({
            ...searchParams,
            page: newPage.toString(),
        });
    };

    const onSearch = (data: SearchFormData) => {
        setSelectedUserId('');
        const newSearchParams: SearchUseAccountrParam = {
            ...searchParams,
            page: '1',
            search: data.search || '',
            'userAreas.area_id': '',
            'userAreaRoles.role_id': '',
        };

        if (data['userAreas'] && data['userAreas']['area_id'].length > 0) {
            newSearchParams['userAreas.area_id'] = data['userAreas']['area_id'].join(',');
        }

        if (data['userAreaRoles'] && data['userAreaRoles']['role_id'].length > 0) {
            newSearchParams['userAreaRoles.role_id'] = data['userAreaRoles']['role_id'].join(',');
        }

        setSearchParams(newSearchParams);
    };

    const handleReset = () => {
        reset({
            search: '',
            'userAreas.area_id': [],
            auth_group: AuthGroups.MOC_MANAGER,
            'userAreaRoles.role_id': [],
        });
        setSearchParams((prevParams) => ({
            ...prevParams,
            page: '1',
            search: '',
            'userAreas.area_id': '',
            'userAreaRoles.role_id': '',
        }));
        setSelectedUserId('');
    };

    const handleRadioChange = (userId: string) => {
        setSelectedUserId(userId);
    };

    const handleAssignUser = () => {
        if (selectedUserId) {
            onUserSelect(selectedUserId);
            handleReset();
            if (onReset) {
                onReset();
            }
        }
    };

    return (
        <div>
            {/* Search Form */}
            <div className="card">
                <div className="card-header">
                    <h4 className="card-title">Search/Filter user</h4>
                </div>
                <div className="card-body">
                    <form onSubmit={handleSubmit(onSearch)}>
                        <div className="row">
                            <div className="col-md-3 col-12">
                                <div className="mb-1">
                                    <label className="form-label">Search user name, email</label>
                                    <Controller
                                        name="search"
                                        control={control}
                                        render={({ field }) => (
                                            <input
                                                {...field}
                                                type="text"
                                                className="form-control"
                                                placeholder="Search user name, email"
                                            />
                                        )}
                                    />
                                </div>
                            </div>
                            <div className="col-md-3 col-12">
                                <div className="mb-1">
                                    <label className="form-label">Select sub area</label>
                                    <Controller
                                        name="userAreas.area_id"
                                        control={control}
                                        render={({ field }) => {
                                            const selectedValues = (field.value || []) as string[];
                                            const selectedOptions = subAreaList.filter((area) =>
                                                selectedValues.includes(area.value)
                                            );
                                            return (
                                                <Select
                                                    isMulti
                                                    options={subAreaList}
                                                    value={selectedOptions}
                                                    onChange={(selectedOptions) => {
                                                        const values = selectedOptions
                                                            ? selectedOptions.map((option) => option.value)
                                                            : [];
                                                        field.onChange(values);
                                                    }}
                                                    className="react-select"
                                                    classNamePrefix="select"
                                                    placeholder="Select sub area"
                                                    styles={{
                                                        control: (base, state) => ({
                                                            ...base,
                                                            borderColor: state.isFocused ? '#00AFF0' : base.borderColor,
                                                            borderWidth: state.isFocused ? '1px' : base.borderWidth,
                                                            boxShadow: state.isFocused
                                                                ? '0 0 0 1px #00AFF0'
                                                                : base.boxShadow,
                                                            '&:hover': {
                                                                borderColor: state.isFocused
                                                                    ? '#00AFF0'
                                                                    : base.borderColor,
                                                            },
                                                        }),
                                                    }}
                                                />
                                            );
                                        }}
                                    />
                                </div>
                            </div>
                            <div className="col-md-3 col-12">
                                <div className="mb-1">
                                    <label className="form-label">Select Approval Role</label>
                                    <Controller
                                        name="userAreaRoles.role_id"
                                        control={control}
                                        render={({ field }) => {
                                            const selectedValues = (field.value || []) as string[];
                                            const selectedOptions = roleList.filter((role) =>
                                                selectedValues.includes(role.value)
                                            );
                                            return (
                                                <Select
                                                    isMulti
                                                    options={roleList}
                                                    value={selectedOptions}
                                                    onChange={(selectedOptions) => {
                                                        const values = selectedOptions
                                                            ? selectedOptions.map((option) => option.value)
                                                            : [];
                                                        field.onChange(values);
                                                    }}
                                                    className="react-select"
                                                    classNamePrefix="select"
                                                    placeholder="Select Approval Role"
                                                    styles={{
                                                        control: (base, state) => ({
                                                            ...base,
                                                            borderColor: state.isFocused ? '#00AFF0' : base.borderColor,
                                                            borderWidth: state.isFocused ? '1px' : base.borderWidth,
                                                            boxShadow: state.isFocused
                                                                ? '0 0 0 1px #00AFF0'
                                                                : base.boxShadow,
                                                            '&:hover': {
                                                                borderColor: state.isFocused
                                                                    ? '#00AFF0'
                                                                    : base.borderColor,
                                                            },
                                                        }),
                                                    }}
                                                />
                                            );
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                        <div className="row">
                            <div className="col-12">
                                <button
                                    type="submit"
                                    className="btn btn-primary me-1"
                                    disabled={isLoading || isRefetching}
                                >
                                    Search
                                </button>
                                <button type="button" className="btn btn-outline-secondary" onClick={handleReset}>
                                    Reset
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            {/* Loading */}
            {(isLoading || isRefetching) && <Spinner />}

            {/* User List Table */}
            {!isLoading && !isRefetching && data && (
                <div className="card">
                    <div className="table-responsive">
                        <table className="table">
                            <thead>
                                <tr>
                                    <th className="text-center"></th>
                                    <th className="text-center">Employee Code</th>
                                    <th>Full Name</th>
                                    <th>Position</th>
                                    <th>Sub area</th>
                                    <th>Authorize Group</th>
                                    <th>Approval Role</th>
                                    <th className="text-center">Status</th>
                                    <th>Email</th>
                                </tr>
                            </thead>
                            <tbody>
                                {data.users_list_paginate.data.map((item: UserAccount) => {
                                    const userRoleNames = item.userAreaRoles.map((role) => role.role.name);
                                    const uniqueRoleNames = [...new Set(userRoleNames)];
                                    return (
                                        <tr key={item.id}>
                                            <td className="text-center">
                                                <input
                                                    type="radio"
                                                    name="selectedUser"
                                                    value={item.id}
                                                    checked={selectedUserId === item.id}
                                                    onChange={() => handleRadioChange(item.id)}
                                                    className="form-check-input"
                                                />
                                            </td>
                                            <td className="text-center">{item.oms_user_id}</td>
                                            <td>{item.full_name}</td>
                                            <td>{item.oms_position_name}</td>
                                            <td>{item.userAreas.map((area) => area.area.name).join(', ')}</td>
                                            <td>{AuthGroupNames.find((area) => area.id === item.auth_group)?.name}</td>
                                            <td>{uniqueRoleNames.join(', ')}</td>
                                            <td className="text-center">
                                                <span
                                                    className={classNames('badge', {
                                                        'badge-light-success': item.is_active,
                                                        'badge-light-secondary': !item.is_active,
                                                    })}
                                                >
                                                    {item.is_active ? 'Active' : 'Inactive'}
                                                </span>
                                            </td>
                                            <td>{item.email}</td>
                                        </tr>
                                    );
                                })}
                            </tbody>
                        </table>
                    </div>
                    <PaginationTable
                        countItem={data.users_list_paginate.totalCount}
                        totalPage={data.users_list_paginate.totalPages}
                        currentPage={data.users_list_paginate.currentPage}
                        handlePageChange={handlePageChange}
                    />
                </div>
            )}

            {/* Footer Buttons */}
            <div className="d-flex justify-content-end mt-3">
                <button type="button" className="btn btn-outline-secondary me-1" onClick={onClose}>
                    Cancel
                </button>
                <button type="button" className="btn btn-primary" onClick={handleAssignUser} disabled={!selectedUserId}>
                    {btnContent}
                </button>
            </div>
        </div>
    );
}
