/* Custom styles for components */

/* React-select focus styles - target all possible class variations */
div[class*="-control"]:focus-within,
div[class*="-control"]:hover,
.css-13cymwt-control:focus-within,
.css-t3ipsp-control,
.css-1pahdxg-control {
  border-color: #00AFF0 !important;
  border-width: 1px !important;
  box-shadow: none !important;
}

/* Target react-select's focused state */
div[class*="-control"][data-focus="true"],
.css-1pahdxg-control,
.css-t3ipsp-control {
  border-color: #00AFF0 !important;
  border-width: 1px !important;
  box-shadow: none !important;
}

/* CustomDateRangePicker focus styles */
.react-datepicker__input-container input:focus,
.react-datepicker-wrapper:focus-within .MuiOutlinedInput-notchedOutline {
  border-color: #00AFF0 !important;
  border-width: 1px !important;
  box-shadow: none !important;
}

/* Override MUI TextField focus styles */
.MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline {
  border-color: #00AFF0 !important;
}

.MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #00AFF0 !important;
  border-width: 1px !important;
}

/* Additional selectors for react-select */
.select__control:hover,
.select__control:focus,
.select__control--is-focused {
  border-color: #00AFF0 !important;
  border-width: 1px !important;
  box-shadow: none !important;
}

/* Ensure all react-select variants are covered */
[class*="select__control"]:hover,
[class*="select__control"]:focus-within {
  border-color: #00AFF0 !important;
  border-width: 1px !important;
  box-shadow: none !important;
}

/* Direct style for react-select component */
.react-select__control:focus-within,
.react-select__control--is-focused {
  border-color: #00AFF0 !important;
  border-width: 1px !important;
  box-shadow: none !important;
}

/* Style for react-select in SearchForm component */
.form-label + div > div[class*="-control"]:focus-within {
  border-color: #00AFF0 !important;
  border-width: 1px !important;
  box-shadow: none !important;
}

/* Style for DateInputShowComponent in CustomDateRangePicker */
.react-datepicker-wrapper .MuiInputBase-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #00AFF0 !important;
  border-width: 1px !important;
  box-shadow: none !important;
}

/* Ensure all MUI TextField variants are covered */
.MuiTextField-root .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #00AFF0 !important;
  border-width: 1px !important;
  box-shadow: none !important;
}

/* Style for react-select in BpmnView component */
.position-relative .select__control:focus-within,
.position-relative .css-1s2u09g-control,
.position-relative div[class*="-control"]:focus-within {
  border-color: #00AFF0 !important;
  border-width: 1px !important;
  box-shadow: none !important;
}

/* Style for all react-select instances */
[id^="react-select"]:focus-within + div {
  border-color: #00AFF0 !important;
  border-width: 1px !important;
  box-shadow: none !important;
}

/* Style for all react-datepicker instances */
.react-datepicker-popper .react-datepicker {
  border-color: #00AFF0;
}

/* Style for react-datepicker selected day */
.react-datepicker__day--selected,
.react-datepicker__day--in-selecting-range,
.react-datepicker__day--in-range {
  background-color: #00AFF0 !important;
}

/* Style for react-datepicker today */
.react-datepicker__day--today {
  border-color: #00AFF0 !important;
}

/* Style for text color in date picker */
.react-datepicker-wrapper .MuiInputBase-input {
  color: #6e6b7b !important;
}

/* Style for placeholder text in date picker */
.react-datepicker-wrapper .MuiInputBase-input::placeholder {
  color: #6e6b7b !important;
  opacity: 0.7;
}

/* Style for text in date picker when focused */
.react-datepicker-wrapper .MuiInputBase-root.Mui-focused .MuiInputBase-input {
  color: #6e6b7b !important;
}
