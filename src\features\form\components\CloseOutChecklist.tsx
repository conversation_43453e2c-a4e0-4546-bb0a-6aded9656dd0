import { Controller, Control } from 'react-hook-form';

interface CloseOutChecklistProps {
    control: Control<any>;
    name: string;
    disabled: boolean;
}

const CLOSE_OUT_ITEMS = [
    'Modification achieves all primary targets and objectives',
    'All relevant staff have been communicated and training',
    'All related documentation has been completed.',
    'Preventive Maintenance (PM) program has been finalized and documented.',
    'PSM Critical (PSMC) Equipment program has been completed and documented.',
    'SAP System: Complete Preventive Maintenance (PM) Program and PSM Critical (PSMC) Equipment.',
    'All MOC records archived as final versions; obsolete documents labeled as superseded',
];

export default function CloseOutChecklist({ control, name, disabled }: CloseOutChecklistProps) {
    return (
        <div className="tw-overflow-auto">
            <table className="tw-w-full tw-border tw-border-collapse">
                <thead className="tw-bg-gray-100">
                    <tr>
                        <th className="tw-border tw-p-2">List</th>
                        <th className="tw-border tw-p-2">Close-Out Item</th>
                        <th className="tw-border tw-p-2 tw-text-center">Complete</th>
                        <th className="tw-border tw-p-2 tw-text-center">N/A</th>
                        <th className="tw-border tw-p-2">Remark</th>
                    </tr>
                </thead>
                <tbody>
                    {CLOSE_OUT_ITEMS.map((item, index) => (
                        <tr key={index}>
                            <td className="tw-border tw-p-1 tw-text-center">{index + 1}</td>
                            <td className="tw-border tw-p-1">{item}</td>

                            <td className="tw-border tw-p-1 tw-text-center">
                                <div className="form-check tw-flex tw-justify-center">
                                    <Controller
                                        name={`${name}.${index}.complete`}
                                        control={control}
                                        render={({ field }) => (
                                            <input
                                                type="checkbox"
                                                checked={!!field.value}
                                                onChange={(e) => field.onChange(e.target.checked)}
                                                className="form-check-input"
                                                disabled={disabled}
                                            />
                                        )}
                                    />
                                </div>
                            </td>

                            <td className="tw-border tw-p-1 tw-text-center">
                                <div className="form-check tw-flex tw-justify-center">
                                    <Controller
                                        name={`${name}.${index}.na`}
                                        control={control}
                                        render={({ field }) => (
                                            <input
                                                type="checkbox"
                                                checked={!!field.value}
                                                onChange={(e) => field.onChange(e.target.checked)}
                                                className="form-check-input"
                                                disabled={disabled}
                                            />
                                        )}
                                    />
                                </div>
                            </td>

                            <td className="tw-border tw-p-1">
                                <Controller
                                    name={`${name}.${index}.remark`}
                                    control={control}
                                    render={({ field }) => (
                                        <textarea
                                            className="tw-w-full tw-outline-none tw-min-h-[40px]"
                                            {...field}
                                            disabled={disabled}
                                        />
                                    )}
                                />
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
