import { useEffect, useRef, useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { Edit, Search, Folder } from 'react-feather';
import ContentHeader from 'components/partials/ContentHeader';
import Spinner from 'components/partials/Spinner';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { UPDATE_USER, UPDATE_USER_AREAS, USER_DETAIL } from '../../../services/UserService';
import { AREAS_LIST } from '../../../services/AreaService';
import { AuthGroupNames, AuthGroups, UserAreas, UserDetailRes } from '../../../types/User';
import Area, { AreaQueryRes, AreaType } from '../../../types/OperationalArea';
import { QUERY_KEY } from '../../../constants/common';
import { keepPreviousData } from '@tanstack/react-query';
import { getFieldInArrayObject, showToast } from '../../../utils/common';
import ModalConfirm from '../../../components/partials/ModalConfirm';
import { FORMAT_DATE, formatDateTime } from '../../../utils/date';

export default function UserEdit() {
    const { id } = useParams<{ id: string }>();
    const [selectedRole, setSelectedRole] = useState<AuthGroups | null>(null);
    const [selectedAreas, setSelectedAreas] = useState<string[]>([]);
    const [areaSearchTerm, setAreaSearchTerm] = useState('');
    const dropdownRef = useRef<HTMLDivElement | null>(null);
    const areaDropdownRef = useRef<HTMLDivElement | null>(null);
    const [uniqueUserRoles, setUniqueUserRoles] = useState<string[]>([]);
    const [showModalConfirm, setShowModalConfirm] = useState(false);
    const [statusActionContent, setStatusActionContent] = useState({
        title: '',
        content: '',
    });

    const { data, isLoading, refetch } = useGraphQLQuery<UserDetailRes>([QUERY_KEY.USER, id], USER_DETAIL, { id }, '', {
        enabled: !!id,
        placeholderData: keepPreviousData,
    });

    useEffect(() => {
        if (data?.user_detail?.is_active) {
            setStatusActionContent({
                title: 'Confirm Deactivate User',
                content: `Are you sure you want to deactivate ${data?.user_detail?.full_name} account? This user will no longer be able to log into the MOC platform.`,
            });
        } else {
            setStatusActionContent({
                title: 'Confirm Activate User',
                content: `Are you sure you want to activate ${data?.user_detail?.full_name} account?`,
            });
        }
    }, [data?.user_detail]);

    useEffect(() => {
        if (data?.user_detail?.userAreaRoles) {
            const roleNames = data.user_detail.userAreaRoles.map((role) => role.role.name);
            const uniqueRoleNames = [...new Set(roleNames)];
            setUniqueUserRoles(uniqueRoleNames);
        }
    }, [data?.user_detail?.userAreaRoles]);

    const { data: areasData } = useGraphQLQuery<AreaQueryRes>([QUERY_KEY.AREAS, AreaType.SUB_AREA], AREAS_LIST, {
        filters: [`type:=(${AreaType.SUB_AREA})`],
    });

    const subAreas: Area[] = areasData?.areas_list || [];

    const updateUserMutation = useGraphQLMutation<
        { update_user: { id: string } },
        { id: string; is_active?: boolean; auth_group?: number }
    >(UPDATE_USER, '', {
        onSuccess: () => {
            showToast(true, ['Update user successfully']);
            refetch();
            setSelectedRole(null);
            setShowModalConfirm(false);
            setTimeout(() => {
                handleCloseDropdown();
            }, 10);
        },
    });

    const updateUserAreasMutation = useGraphQLMutation<
        { update_user_areas: boolean },
        { id: string; area_ids: string[] }
    >(UPDATE_USER_AREAS, '', {
        onSuccess: () => {
            showToast(true, ['Update user area successfully']);
            refetch();
            setSelectedAreas([]);
            setTimeout(() => {
                handleCloseAreaDropdown();
            }, 10);
        },
    });

    const user = data?.user_detail;

    useEffect(() => {
        if (user?.userAreas) {
            setSelectedAreas(user.userAreas.map((ua) => ua.area_id.toString()));
        }
    }, [user?.userAreas]);

    const handleToggleStatus = () => {
        if (!user || !id) return;

        updateUserMutation.mutate({
            id: id,
            is_active: !user.is_active,
        });
    };

    const handleSelectRole = (authGroup: AuthGroups) => {
        setSelectedRole(authGroup);
    };

    const handleUpdateRole = () => {
        if (!id || selectedRole === null) return;

        updateUserMutation.mutate({
            id: id,
            auth_group: selectedRole,
        });
    };

    const handleCloseDropdown = () => {
        const editRoleButton = dropdownRef.current?.querySelector('.dropdown-toggle') as HTMLButtonElement;
        if (editRoleButton) {
            const bsDropdown = new (window as any).bootstrap.Dropdown(editRoleButton);
            bsDropdown.hide();
        }
    };

    const handleCloseAreaDropdown = () => {
        const editAreaButton = areaDropdownRef.current?.querySelector('.dropdown-toggle') as HTMLButtonElement;
        if (editAreaButton) {
            const bsDropdown = new (window as any).bootstrap.Dropdown(editAreaButton);
            bsDropdown.hide();
        }
    };

    const handleSelectArea = (areaId: string) => {
        setSelectedAreas([areaId]);
    };

    const handleUpdateAreas = () => {
        if (!id) return;

        updateUserAreasMutation.mutate({
            id: id,
            area_ids: selectedAreas,
        });
    };

    const getCurrentSelectedRole = () => selectedRole ?? user?.auth_group;

    const getCurrentSelectedAreas = () =>
        selectedAreas.length > 0 ? selectedAreas : user?.userAreas?.map((ua) => ua.area_id.toString()) || [];

    const getGroupedAreas = () => {
        if (!user?.userAreas) return [];

        const grouped: { [key: string]: { zone: string; mainArea: string; subAreas: string[] } } = {};

        user.userAreas.forEach((userArea: UserAreas) => {
            const subArea = userArea.area;
            const mainArea = subArea.parentArea;
            const zone = mainArea?.parentArea;

            if (zone && mainArea && subArea) {
                const key = `${zone.id}-${mainArea.id}`;

                if (!grouped[key]) {
                    grouped[key] = {
                        zone: zone.name,
                        mainArea: mainArea.name,
                        subAreas: [],
                    };
                }

                const subAreaDisplay = subArea.code ? `${subArea.name} (${subArea.code})` : subArea.name;
                if (!grouped[key].subAreas.includes(subAreaDisplay)) {
                    grouped[key].subAreas.push(subAreaDisplay);
                }
            }
        });

        return Object.values(grouped);
    };

    const filteredAreas = subAreas.filter(
        (area) =>
            area.name.toLowerCase().includes(areaSearchTerm.toLowerCase()) ||
            area.code?.toLowerCase().includes(areaSearchTerm.toLowerCase())!
    );

    const getContextMenuText = () => {
        if (updateUserMutation.isPending) return 'Processing...';
        return user?.is_active ? 'Deactivate user' : 'Activate user';
    };

    if (isLoading) {
        return <Spinner />;
    }

    if (!user) {
        return <div className="alert alert-danger">User not found!</div>;
    }

    const authGroupName = getFieldInArrayObject(AuthGroupNames, user.auth_group, 'name', 'Unknown');

    return (
        <>
            <Helmet>
                <title>Edit User</title>
            </Helmet>
            <ContentHeader
                title={
                    <>
                        <Link to="/user/list">User Account</Link>
                    </>
                }
                breadcrumbs={[
                    {
                        text: `Edit User`,
                    },
                ]}
                contextMenu={[
                    {
                        text: getContextMenuText(),
                        fnCallBack: {
                            actionMenu: () => setShowModalConfirm(true),
                        },
                        btnClassName: user?.is_active ? 'btn-danger' : 'btn-primary',
                    },
                ]}
            />
            <div className="content-body">
                <div className="row">
                    <div className="col-12">
                        <div className="mb-4 card">
                            <div className="card-header">
                                <h5 className="mb-0 card-title">General Information</h5>
                            </div>
                            <div className="card-body">
                                <div className="row">
                                    <div className="col-md-6">
                                        <div className="mb-1 d-flex">
                                            <label className="alert-link me-2" style={{ minWidth: '120px' }}>
                                                Full Name
                                            </label>
                                            <div>{user.full_name}</div>
                                        </div>
                                        <div className="mb-1 d-flex">
                                            <label className="alert-link me-2" style={{ minWidth: '120px' }}>
                                                Position
                                            </label>
                                            <div>{user.oms_position_name}</div>
                                        </div>
                                        <div className="mb-1 d-flex">
                                            <label className="alert-link me-2" style={{ minWidth: '120px' }}>
                                                Email
                                            </label>
                                            <div>{user.email}</div>
                                        </div>
                                    </div>
                                    <div className="col-md-6">
                                        <div className="mb-1 d-flex">
                                            <label className="alert-link me-2" style={{ minWidth: '120px' }}>
                                                Approval Role
                                            </label>
                                            <div>{uniqueUserRoles.join(', ')}</div>
                                        </div>
                                        <div className="mb-1 d-flex">
                                            <label className="alert-link me-2" style={{ minWidth: '120px' }}>
                                                Employee Code
                                            </label>
                                            <div>{user.oms_emp_code}</div>
                                        </div>
                                        <div className="mb-1 d-flex">
                                            <label className="alert-link me-2" style={{ minWidth: '120px' }}>
                                                Status
                                            </label>
                                            <div>
                                                <span
                                                    className={`badge ${user.is_active ? 'bg-success' : 'bg-danger'}`}
                                                >
                                                    {user.is_active ? 'Active' : 'Inactive'}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="col-md-6">
                                        <div className="mb-1 d-flex">
                                            <label className="alert-link me-2" style={{ minWidth: '120px' }}>
                                                Creation At
                                            </label>
                                            <div>{formatDateTime(user.created_at!, FORMAT_DATE.SHOW_DATE_MINUTE)}</div>
                                        </div>
                                        <div className="mb-1 d-flex">
                                            <label className="alert-link me-2" style={{ minWidth: '120px' }}>
                                                Creation By
                                            </label>
                                            <div>{user.creator?.full_name}</div>
                                        </div>
                                    </div>
                                    <div className="col-md-6">
                                        <div className="mb-1 d-flex">
                                            <label className="alert-link me-2" style={{ minWidth: '120px' }}>
                                                Last Update At
                                            </label>
                                            <div>{formatDateTime(user.updated_at!, FORMAT_DATE.SHOW_DATE_MINUTE)}</div>
                                        </div>
                                        <div className="mb-1 d-flex">
                                            <label className="alert-link me-2" style={{ minWidth: '120px' }}>
                                                Last Update By
                                            </label>
                                            <div>{user.updater?.full_name}</div>
                                        </div>
                                    </div>
                                    <div className="col-md-6">
                                        <div className="mb-1 d-flex">
                                            <label className="alert-link me-2" style={{ minWidth: '120px' }}>
                                                Authorize Group
                                            </label>
                                            <div
                                                className="items-center gap-2 d-flex tw-justify-items-center"
                                                ref={dropdownRef}
                                            >
                                                <span>{authGroupName}</span>
                                                <button
                                                    type="button"
                                                    className="btn p-0 dropdown-toggle waves-effect waves-light tw-text-[#00AFF0] tw-opacity-80 hover:tw-text-[#00AFF0] hover:tw-opacity-100"
                                                    data-bs-toggle="dropdown"
                                                    aria-expanded="false"
                                                    data-bs-auto-close="outside"
                                                    disabled={updateUserMutation.isPending}
                                                    id="dropdownEditAuthGroup"
                                                >
                                                    <Edit size={14} />
                                                </button>
                                                <ul
                                                    className="dropdown-menu"
                                                    aria-labelledby="dropdownEditAuthGroup"
                                                    style={{ width: '300px' }}
                                                >
                                                    <li className="p-1">
                                                        <strong>Select and update user authorize group</strong>
                                                    </li>
                                                    <li>
                                                        <hr className="dropdown-divider" />
                                                    </li>
                                                    {AuthGroupNames.map((group) => (
                                                        <li
                                                            key={group.id}
                                                            onClick={() =>
                                                                !updateUserMutation.isPending &&
                                                                handleSelectRole(group.id as AuthGroups)
                                                            }
                                                            style={{
                                                                cursor: updateUserMutation.isPending
                                                                    ? 'not-allowed'
                                                                    : 'pointer',
                                                            }}
                                                        >
                                                            <div className="dropdown-item">
                                                                <div className="form-check">
                                                                    <input
                                                                        className="form-check-input"
                                                                        type="radio"
                                                                        name="authGroupRadio"
                                                                        id={`auth-radio-${group.id}`}
                                                                        checked={getCurrentSelectedRole() === group.id}
                                                                        onChange={(e) => {
                                                                            e.stopPropagation();
                                                                            handleSelectRole(group.id as AuthGroups);
                                                                        }}
                                                                        disabled={updateUserMutation.isPending}
                                                                    />
                                                                    <label
                                                                        className="form-check-label"
                                                                        htmlFor={`auth-radio-${group.id}`}
                                                                        onClick={(e) => {
                                                                            e.stopPropagation();
                                                                            if (!updateUserMutation.isPending) {
                                                                                handleSelectRole(
                                                                                    group.id as AuthGroups
                                                                                );
                                                                            }
                                                                        }}
                                                                    >
                                                                        {group.name}
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        </li>
                                                    ))}
                                                    <li>
                                                        <hr className="dropdown-divider" />
                                                    </li>
                                                    <li className="p-1">
                                                        <div className="gap-2 d-flex justify-content-end">
                                                            <button
                                                                type="button"
                                                                className="btn btn-outline-secondary btn-sm"
                                                                onClick={handleCloseDropdown}
                                                                disabled={updateUserMutation.isPending}
                                                            >
                                                                Close
                                                            </button>
                                                            <button
                                                                type="button"
                                                                className="btn btn-primary btn-sm"
                                                                onClick={handleUpdateRole}
                                                                disabled={
                                                                    updateUserMutation.isPending ||
                                                                    selectedRole === null
                                                                }
                                                            >
                                                                {updateUserMutation.isPending
                                                                    ? 'Updating...'
                                                                    : 'Update'}
                                                            </button>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {(user.auth_group === AuthGroups.MOC_MANAGER || user.auth_group === AuthGroups.MOC_USER) && (
                            <div className="mb-4 card">
                                <div className="card-header d-flex justify-content-between align-items-center">
                                    <h5 className="mb-0 card-title">Operational Area Information</h5>
                                    <div className="btn-group" ref={areaDropdownRef}>
                                        <button
                                            type="button"
                                            className="btn btn-outline-primary btn-sm dropdown-toggle waves-effect waves-light"
                                            data-bs-toggle="dropdown"
                                            aria-expanded="false"
                                            data-bs-auto-close="outside"
                                            disabled={updateUserAreasMutation.isPending}
                                        >
                                            Edit Area
                                        </button>
                                        <ul
                                            className="dropdown-menu"
                                            aria-labelledby="dropdownMenuClickableInside"
                                            style={{ width: '400px' }}
                                        >
                                            <li className="p-1">
                                                <strong>Select and update sub area</strong>
                                            </li>
                                            <li className="p-1">
                                                <div className="input-group" style={{ maxWidth: '100%' }}>
                                                    <span className="input-group-text">
                                                        <Search size={16} />
                                                    </span>
                                                    <input
                                                        type="text"
                                                        className="form-control"
                                                        placeholder="search sub area"
                                                        value={areaSearchTerm}
                                                        onChange={(e) => setAreaSearchTerm(e.target.value)}
                                                    />
                                                </div>
                                            </li>
                                            <li>
                                                <hr className="dropdown-divider" />
                                            </li>
                                            <li>
                                                <div
                                                    style={{
                                                        maxHeight: '200px',
                                                        overflowY: 'auto',
                                                        padding: '0.5rem',
                                                    }}
                                                >
                                                    {filteredAreas.map((area) => (
                                                        <div
                                                            key={area.id}
                                                            style={{
                                                                cursor: updateUserAreasMutation.isPending
                                                                    ? 'not-allowed'
                                                                    : 'pointer',
                                                            }}
                                                            className="p-1 rounded d-flex align-items-start hover-bg-light"
                                                        >
                                                            <div
                                                                className="d-flex align-items-center"
                                                                style={{ minWidth: '30px', marginTop: '2px' }}
                                                            >
                                                                <input
                                                                    type="radio"
                                                                    className="form-check-input me-2"
                                                                    checked={getCurrentSelectedAreas().includes(
                                                                        area.id!.toString()
                                                                    )}
                                                                    onChange={(e) => {
                                                                        e.stopPropagation();
                                                                        handleSelectArea(area.id);
                                                                    }}
                                                                    disabled={updateUserAreasMutation.isPending}
                                                                    id={`area-${area.id}`}
                                                                />
                                                                <span className="me-1">
                                                                    <Folder size={16} />
                                                                </span>
                                                            </div>
                                                            <label
                                                                htmlFor={`area-${area.id}`}
                                                                className="form-check-label"
                                                                style={{
                                                                    flex: 1,
                                                                    whiteSpace: 'normal',
                                                                    wordBreak: 'break-word',
                                                                }}
                                                                title={`${area.name}${
                                                                    area.code ? ` (${area.code})` : ''
                                                                }`}
                                                            >
                                                                {area.name}
                                                                {area.code && ` (${area.code})`}
                                                            </label>
                                                        </div>
                                                    ))}
                                                </div>
                                            </li>
                                            <li>
                                                <hr className="dropdown-divider" />
                                            </li>
                                            <li className="p-1">
                                                <div className="gap-2 d-flex justify-content-end">
                                                    <button
                                                        type="button"
                                                        className="btn btn-outline-secondary btn-sm"
                                                        onClick={handleCloseAreaDropdown}
                                                        disabled={updateUserAreasMutation.isPending}
                                                    >
                                                        Close
                                                    </button>
                                                    <button
                                                        type="button"
                                                        className="btn btn-primary btn-sm"
                                                        onClick={handleUpdateAreas}
                                                        disabled={
                                                            selectedAreas.length === 0 ||
                                                            updateUserAreasMutation.isPending
                                                        }
                                                    >
                                                        {updateUserAreasMutation.isPending ? 'Updating...' : 'Update'}
                                                    </button>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div className="card-body">
                                    {getGroupedAreas().length > 0 ? (
                                        getGroupedAreas().map((group, index) => (
                                            <div key={index} className="mb-3">
                                                <div className="mb-1 d-flex">
                                                    <label className="alert-link me-2" style={{ minWidth: '120px' }}>
                                                        Zone
                                                    </label>
                                                    <div>{group.zone}</div>
                                                </div>
                                                <div className="mb-1 d-flex">
                                                    <label className="alert-link me-2" style={{ minWidth: '120px' }}>
                                                        Main Area
                                                    </label>
                                                    <div>{group.mainArea}</div>
                                                </div>
                                                <div className="d-flex">
                                                    <label className="alert-link me-2" style={{ minWidth: '120px' }}>
                                                        Sub Area
                                                    </label>
                                                    <div>{group.subAreas.join(', ')}</div>
                                                </div>
                                                {index < getGroupedAreas().length - 1 && <hr className="my-3" />}
                                            </div>
                                        ))
                                    ) : (
                                        <div className="text-muted">No areas assigned</div>
                                    )}
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            <ModalConfirm
                show={showModalConfirm}
                text={statusActionContent.content}
                btnDisabled={updateUserAreasMutation.isPending}
                changeShow={setShowModalConfirm}
                submitAction={handleToggleStatus}
                textTitle={statusActionContent.title}
            />
        </>
    );
}
