import { gql } from 'graphql-request';

export const FORM_LIST = gql`
    query Form_list($page: Int!, $limit: Int!, $search: String, $filters: [String!]) {
        form_list(input: { page: $page, limit: $limit, search: $search, filters: $filters }) {
            currentPage
            totalPages
            totalCount
            data {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                name
                schema
                description
                status
                version
                creator {
                    id
                    created_by
                    updated_by
                    created_at
                    updated_at
                    deleted_at
                    oms_user_id
                    username
                    email
                    full_name
                    is_active
                    oms_emp_code
                    oms_organization_id
                    oms_organization_name
                    oms_ad_account
                    oms_position_name
                    oms_company_code
                    oms_company_name
                    oms_div_name
                    oms_dep_name
                    auth_group
                }
                updater {
                    id
                    created_by
                    updated_by
                    created_at
                    updated_at
                    deleted_at
                    oms_user_id
                    username
                    email
                    full_name
                    is_active
                    oms_emp_code
                    oms_organization_id
                    oms_organization_name
                    oms_ad_account
                    oms_position_name
                    oms_company_code
                    oms_company_name
                    oms_div_name
                    oms_dep_name
                    auth_group
                }
                workflow_forms {
                    id
                    form_id
                    task_key
                    task_name
                    is_first_form
                    workflow_definition {
                        id
                        created_by
                        updated_by
                        created_at
                        updated_at
                        deleted_at
                        name
                        bpmnXml
                        status
                        camunda_key
                        camunda_id
                        version
                    }
                }
            }
        }
    }
`;

export const FORM_DETAIL = gql`
    query Form_detail($id: String!) {
        form_detail(id: $id) {
            id
            created_by
            updated_by
            created_at
            updated_at
            deleted_at
            name
            schema
            description
            status
            version
            creator {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                oms_user_id
                username
                email
                full_name
                is_active
                oms_emp_code
                oms_organization_id
                oms_organization_name
                oms_ad_account
                oms_position_name
                oms_company_code
                oms_company_name
                oms_div_name
                oms_dep_name
                auth_group
            }
            workflow_forms {
                id
                form_id
                task_key
                task_name
                is_first_form
                workflow_definition {
                    id
                    created_by
                    updated_by
                    created_at
                    updated_at
                    deleted_at
                    name
                    bpmnXml
                    status
                    camunda_key
                    camunda_id
                    version
                }
            }
            updater {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                oms_user_id
                username
                email
                full_name
                is_active
                oms_emp_code
                oms_organization_id
                oms_organization_name
                oms_ad_account
                oms_position_name
                oms_company_code
                oms_company_name
                oms_div_name
                oms_dep_name
                auth_group
            }
        }
    }
`;

export const FORM_CREATE = gql`
    mutation CreateForm($input: CreateFormInput!) {
        form_create(input: $input) {
            id
            name
            schema
            description
            status
            created_by
            updated_by
            created_at
            updated_at
            deleted_at
        }
    }
`;

export const FORM_UPDATE = gql`
    mutation UpdateForm($input: UpdateFormInput!) {
        form_update(input: $input) {
            id
            name
            schema
            description
            status
            created_by
            updated_by
            created_at
            updated_at
            deleted_at
        }
    }
`;

export const DELETE_FORM = gql`
    mutation Form_delete($id: String!) {
        form_delete(id: $id)
    }
`;

export const FORM_ACTIVE = gql`
    mutation Form_activate($id: String!) {
        form_activate(id: $id) {
            id
        }
    }
`;

export const FORM_INACTIVE = gql`
    mutation Form_inactivate($id: String!) {
        form_inactivate(id: $id) {
            id
        }
    }
`;

export const FORM_DASHBOARD = gql`
    query Form_dashboard {
        form_dashboard {
            activated
            inactivated
            draft
            total
        }
    }
`;
