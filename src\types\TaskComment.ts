import { FILTER_CONDITIONS } from '../constants/common';
import { BaseModelString, BaseSearch, FilterConfig } from './common/index';
import { UserAccount } from './User';
import { UserTaskEntity } from './Workflow';

export default interface TaskComment extends BaseModelString {
    user_task_id: string;
    content: string;
    userTask: UserTaskEntity;
    creator: UserAccount;
}

export interface TaskCommentListQuery {
    task_comments_list: TaskComment[];
}

export interface TaskCommentCreate {
    task_comments_create: TaskComment;
}

export interface TaskCommentDelete {
    task_comments_delete: boolean;
}

export interface SearchTaskComment extends BaseSearch {
    user_task_id?: string;
}

export const taskCommentFilterConfig: FilterConfig = {
    user_task_id: { key: 'user_task_id', operator: FILTER_CONDITIONS.EQUAL },
};

export interface ITaskCommentGroup {
    workflow_step_id: string;
    workflow_step_name: string;
    workflow_step_order: number;
    comments: TaskComment[];
}
