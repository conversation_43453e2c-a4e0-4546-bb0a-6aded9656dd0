import React, { useState, useRef, useEffect } from 'react';
import { Edit } from 'react-feather';
import classNames from 'classnames';

interface ColorPickerProps {
    value: string;
    onChange: (color: string) => void;
    label: string;
    defaultColor?: string;
}

const ColorPicker: React.FC<ColorPickerProps> = ({ value, onChange, label, defaultColor = '#000000' }) => {
    const [isOpen, setIsOpen] = useState(false);
    const [hexInput, setHexInput] = useState(value || defaultColor);
    const colorInputRef = useRef<HTMLInputElement>(null);
    const dropdownRef = useRef<HTMLDivElement>(null);

    // Predefined colors
    const presetColors = [
        '#5e5873',
        '#00AFF0',
        '#28c76f',
        '#ff9f43',
        '#ea5455',
        '#7367f0',
        '#000000',
        '#ffffff',
        '#6c757d',
        '#f8f9fa',
        '#e9ecef',
        '#dee2e6',
        '#ced4da',
        '#adb5bd',
        '#6c757d',
        '#495057',
        '#343a40',
        '#212529',
    ];

    useEffect(() => {
        setHexInput(value || defaultColor);
    }, [value, defaultColor]);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isOpen]);

    const handleColorChange = (color: string) => {
        setHexInput(color);
        onChange(color);
    };

    const handleHexInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newValue = e.target.value;
        setHexInput(newValue);

        // Validate hex color format
        if (/^#[0-9A-F]{6}$/i.test(newValue) || /^#[0-9A-F]{3}$/i.test(newValue)) {
            onChange(newValue);
        }
    };

    const handleHexInputBlur = () => {
        // If invalid hex, revert to current value
        if (!/^#[0-9A-F]{6}$/i.test(hexInput) && !/^#[0-9A-F]{3}$/i.test(hexInput)) {
            setHexInput(value || defaultColor);
        }
    };

    const isValidHex = /^#[0-9A-F]{6}$/i.test(hexInput) || /^#[0-9A-F]{3}$/i.test(hexInput);

    return (
        <div className="tw-relative" ref={dropdownRef}>
            <label className="form-label tw-text-[14px] tw-mb-2 tw-block">{label}</label>

            <div className="tw-flex tw-gap-2">
                {/* Color preview and picker button */}
                <button
                    type="button"
                    onClick={() => setIsOpen(!isOpen)}
                    className="tw-w-10 tw-h-[38px] tw-border tw-border-[#d8d6de] tw-rounded-md tw-flex tw-items-center tw-justify-center tw-bg-white hover:tw-border-[#00AFF0] tw-transition-colors"
                    style={{ backgroundColor: isValidHex ? hexInput : defaultColor }}
                >
                    <Edit
                        size={16}
                        className={classNames('tw-text-white', {
                            '!tw-text-gray-600': hexInput === '#ffffff' || hexInput === '#fff',
                        })}
                    />
                </button>

                {/* Hex input */}
                <input
                    type="text"
                    value={hexInput}
                    onChange={handleHexInputChange}
                    onBlur={handleHexInputBlur}
                    placeholder="#000000"
                    className={classNames('form-control tw-flex-1', {
                        'tw-border-red-500': !isValidHex && hexInput !== '',
                    })}
                    maxLength={7}
                />
            </div>

            {/* Color picker dropdown */}
            {isOpen && (
                <div className="tw-absolute tw-top-full tw-left-0 tw-mt-1 tw-bg-white tw-border tw-border-[#d8d6de] tw-rounded-lg tw-shadow-lg tw-p-3 tw-z-50 tw-w-64">
                    {/* Native color picker */}
                    <div className="tw-mb-3">
                        <input
                            ref={colorInputRef}
                            type="color"
                            value={isValidHex ? hexInput : defaultColor}
                            onChange={(e) => handleColorChange(e.target.value)}
                            className="tw-w-full tw-h-10 tw-border tw-border-[#d8d6de] tw-rounded-md tw-cursor-pointer"
                        />
                    </div>

                    {/* Preset colors */}
                    <div className="tw-mb-2">
                        <span className="tw-text-xs tw-text-gray-600 tw-mb-2 tw-block">Preset Colors</span>
                        <div className="tw-grid tw-grid-cols-6 tw-gap-1">
                            {presetColors.map((color) => (
                                <button
                                    key={color}
                                    type="button"
                                    onClick={() => handleColorChange(color)}
                                    className={classNames(
                                        'tw-w-8 tw-h-8 tw-rounded tw-border-2 tw-cursor-pointer hover:tw-scale-110 tw-transition-transform',
                                        {
                                            'tw-border-[#00AFF0]': hexInput === color,
                                            'tw-border-gray-300': hexInput !== color,
                                        }
                                    )}
                                    style={{ backgroundColor: color }}
                                    title={color}
                                />
                            ))}
                        </div>
                    </div>

                    {/* Current color info */}
                    <div className="tw-text-xs tw-text-gray-600 tw-pt-2 tw-border-t tw-border-gray-200">
                        Current: {isValidHex ? hexInput : 'Invalid color'}
                    </div>
                </div>
            )}
        </div>
    );
};

export default ColorPicker;
